{
	frankenphp
	order php_server before file_server
}

# The domain name of your server - change to your actual domain in production
localhost {
	# Set the webroot to the public/ directory
	root * public/
	# Enable compression
	encode zstd br gzip
	# Execute PHP files from the public/ directory and serve assets
	php_server {
		try_files {path} index.php
	}
	# Enable HTTP/2 Server Push for Inertia assets
	header Link "</build/assets/app.css>; rel=preload; as=style"
	header Link "</build/assets/app.js>; rel=preload; as=script"
}
