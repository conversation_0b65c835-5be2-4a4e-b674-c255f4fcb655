<?php

use App\Models\ReportBatch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_d02', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(ReportBatch::class, 'report_batch_id')->constrained()->cascadeOnDelete();
            $table->string('flag_detail')->nullable();
            $table->string('nomor_cif_debitur')->nullable();
            $table->string('nomor_identitas_badan_usaha')->nullable();
            $table->string('nama_badan_usaha')->nullable();
            $table->string('kode_bentuk_badan_usaha')->nullable();
            $table->string('tempat_pendirian')->nullable();
            $table->string('nomor_akta_pendirian')->nullable();
            $table->date('tanggal_akta_pendirian')->nullable();
            $table->string('nomor_akta_perubahan_terakhir')->nullable();
            $table->date('tanggal_akta_perubahan_terakhir')->nullable();
            $table->string('nomor_telepon')->nullable();
            $table->string('nomor_telepon_seluler')->nullable();
            $table->string('alamat_email')->nullable();
            $table->text('alamat')->nullable();
            $table->string('kelurahan')->nullable();
            $table->string('kecamatan')->nullable();
            $table->string('kode_sandi_kab_kota')->nullable();
            $table->string('kode_pos')->nullable();
            $table->string('kode_negara_domisili')->nullable();
            $table->string('kode_bidang_usaha')->nullable();
            $table->string('kode_hubungan_dengan_pelapor')->nullable();
            $table->boolean('melanggar_bmpk_bmpd_bmpp')->nullable();
            $table->boolean('melampaui_bmpk_bmpd_bmpp')->nullable();
            $table->boolean('go_public')->nullable();
            $table->string('kode_golongan_debitur')->nullable();
            $table->string('peringkat_debitur')->nullable();
            $table->string('lembaga_pemeringkat')->nullable();
            $table->date('tanggal_pemeringkatan')->nullable();
            $table->string('nama_group_debitur')->nullable();
            $table->string('kode_kantor_cabang')->nullable();
            $table->string('operasi_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_d02');
    }
};
