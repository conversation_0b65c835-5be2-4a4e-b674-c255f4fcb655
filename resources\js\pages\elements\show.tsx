import { Head } from '@inertiajs/react';
import { Link } from '@inertiajs/react';
import { <PERSON><PERSON><PERSON><PERSON>, Pencil, Trash2 } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

interface Template {
    id: number;
    name: string;
    alias: string;
    description: string;
}

interface ReportElement {
    id: number;
    label: string;
    field: string;
    type: string;
    created_at: string;
    updated_at: string;
    templates: Template[];
}

interface ElementShowProps {
    element: ReportElement;
}

export default function ElementShow({ element }: ElementShowProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Elements',
            href: '/elements',
        },
        {
            title: element.label,
            href: route('elements.show', { element: element.id }),
        },
    ];
    
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(date);
    };
    
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Element - ${element.label}`} />
            
            <div className="flex flex-col gap-6 p-4">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">{element.label}</h1>
                        <p className="text-muted-foreground">Field: <code className="bg-muted px-1 py-0.5 rounded">{element.field}</code></p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href={route('elements.index')}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Elements
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={route('elements.edit', { element: element.id })}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit Element
                            </Link>
                        </Button>
                    </div>
                </div>
                
                <Card>
                    <CardHeader>
                        <CardTitle>Element Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">Label</h3>
                                    <p className="mt-1">{element.label}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">Field</h3>
                                    <p className="mt-1 font-mono">{element.field}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">Type</h3>
                                    <div className="mt-1">
                                        <Badge variant="outline">{element.type}</Badge>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">Used In</h3>
                                    <p className="mt-1">
                                        {element.templates.length} template{element.templates.length !== 1 ? 's' : ''}
                                    </p>
                                </div>
                            </div>
                            
                            <Separator />
                            
                            <div>
                                <div className="flex justify-between items-center mb-2">
                                    <h3 className="text-sm font-medium text-muted-foreground">Templates Using This Element</h3>
                                </div>
                                
                                {element.templates.length === 0 ? (
                                    <div className="text-center py-8 border border-dashed rounded-md">
                                        <p className="text-muted-foreground">This element is not used in any templates yet.</p>
                                    </div>
                                ) : (
                                    <div className="border rounded-md overflow-hidden">
                                        <table className="w-full">
                                            <thead>
                                                <tr className="bg-muted/50">
                                                    <th className="px-4 py-2 text-left text-sm font-medium">ID</th>
                                                    <th className="px-4 py-2 text-left text-sm font-medium">Name</th>
                                                    <th className="px-4 py-2 text-left text-sm font-medium">Alias</th>
                                                    <th className="px-4 py-2 text-left text-sm font-medium">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {element.templates.map((template) => (
                                                    <tr key={template.id} className="border-t">
                                                        <td className="px-4 py-2 text-sm">{template.id}</td>
                                                        <td className="px-4 py-2 text-sm font-medium">{template.name}</td>
                                                        <td className="px-4 py-2 text-sm">{template.alias}</td>
                                                        <td className="px-4 py-2 text-sm">
                                                            <Button variant="outline" size="sm" asChild>
                                                                <Link href={route('templates.show', { template: template.id })}>
                                                                    View
                                                                </Link>
                                                            </Button>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </div>
                            
                            <Separator />
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">Created At</h3>
                                    <p className="mt-1">{formatDate(element.created_at)}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">Last Updated</h3>
                                    <p className="mt-1">{formatDate(element.updated_at)}</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <div className="flex justify-between items-center">
                    <Button variant="outline" asChild>
                        <Link href={route('elements.index')}>
                            Back to Elements
                        </Link>
                    </Button>
                    
                    {element.templates.length === 0 && (
                        <Button variant="destructive" asChild>
                            <Link 
                                href={route('elements.destroy', { element: element.id })} 
                                method="delete" 
                                as="button"
                            >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Element
                            </Link>
                        </Button>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
