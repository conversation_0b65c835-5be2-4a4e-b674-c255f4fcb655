import { useEffect, useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { toast } from 'sonner';
import { Trash2, Plus, Pencil, GripVertical } from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { ReportType } from '@/types/reports';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

interface EditProps {
  reportType: ReportType;
}

// Komponen untuk item yang bisa di-drag
interface SortableFieldItemProps {
  field: {
    name: string;
    position: number;
    type: string;
  };
  index: number;
  onRemove: (index: number) => void;
}

function SortableFieldItem({ field, index, onRemove }: SortableFieldItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition
  } = useSortable({ id: field.position.toString() });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="bg-card border rounded-md p-3 mb-2 flex items-center gap-2"
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing p-1 flex items-center"
      >
        <GripVertical className="h-5 w-5 text-muted-foreground" />
        <span className="sr-only">Drag</span>
      </div>
      <div className="flex-1">
        <div className="font-medium">{field.name}</div>
        <div className="text-sm text-muted-foreground">Posisi: {field.position}</div>
      </div>
      <Badge variant="outline" className="mr-2">
        {field.type}
      </Badge>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onRemove(index)}
        className="h-8 w-8 p-0 text-destructive"
      >
        <Trash2 className="h-4 w-4" />
        <span className="sr-only">Hapus</span>
      </Button>
    </div>
  );
}

export default function ReportTypeEdit({ reportType }: EditProps) {
  // State untuk form field baru
  const [fieldName, setFieldName] = useState<string>('');
  const [fieldPosition, setFieldPosition] = useState<string>('');
  const [fieldType, setFieldType] = useState<string>('');
  const [fields, setFields] = useState<any[]>(
    reportType.field_definitions?.fields || []
  );

  const { data, setData, put, processing, errors } = useForm({
    name: reportType.name,
    description: reportType.description || '',
    field_definitions: reportType.field_definitions || { fields: [] },
  });

  useEffect(() => {
    setFields(reportType.field_definitions?.fields || []);
  }, [reportType]);

  // Sensor untuk drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handler untuk drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setFields((items) => {
        // Temukan indeks item yang di-drag dan item tujuan
        const oldIndex = items.findIndex(item => item.position.toString() === active.id);
        const newIndex = items.findIndex(item => item.position.toString() === over.id);

        // Pindahkan item
        const newItems = arrayMove(items, oldIndex, newIndex);

        // Perbarui posisi semua item
        const updatedItems = newItems.map((item, index) => ({
          ...item,
          position: index + 1, // Posisi dimulai dari 1
        }));

        // Perbarui form data
        setData((prevData) => ({
          ...prevData,
          field_definitions: {
            fields: updatedItems,
          },
        }));

        return updatedItems;
      });
    }
  };

  const handleChange = (name: string, value: any) => {
    setData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleAddField = () => {
    // Validate field
    if (!fieldName || !fieldPosition || !fieldType) {
      toast.error('Semua field harus diisi.');
      return;
    }

    const position = parseInt(fieldPosition);

    // Check for duplicate position
    const existingPosition = fields.find((f) => f.position === position);
    if (existingPosition) {
      toast.error('Posisi field sudah digunakan.');
      return;
    }

    const newField = {
      name: fieldName,
      position: position,
      type: fieldType,
    };

    // Add field
    const newFields = [...fields, newField];

    // Urutkan field berdasarkan posisi
    const sortedFields = newFields.sort((a, b) => a.position - b.position);

    setFields(sortedFields);

    // Update form data
    setData((prevData) => ({
      ...prevData,
      field_definitions: {
        fields: sortedFields,
      },
    }));

    // Reset field form
    setFieldName('');
    setFieldPosition('');
    setFieldType('');
  };

  const handleRemoveField = (index: number) => {
    const newFields = [...fields];
    newFields.splice(index, 1);

    // Perbarui posisi field setelah menghapus
    const updatedFields = newFields.map((field, idx) => ({
      ...field,
      position: idx + 1, // Posisi dimulai dari 1
    }));

    setFields(updatedFields);

    // Update form data
    setData((prevData) => ({
      ...prevData,
      field_definitions: {
        fields: updatedFields,
      },
    }));
  };

  const handleSubmit = () => {
    put(route('report-types.update', reportType.id));
  };

  // Tabel field akan menggunakan Shadcn UI Table

  return (
    <AppLayout>
      <Head title={`Edit Tipe Report ${reportType.id}`} />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6 bg-white border-b border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold">
              Edit Tipe Report: {reportType.id}
            </h2>
            <Link href={route('report-types.show', reportType.id)}>
              <Button variant="outline">Kembali ke Detail</Button>
            </Link>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Informasi Tipe Report</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="id">ID</Label>
                    <Input
                      id="id"
                      value={reportType.id}
                      disabled
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">Nama</Label>
                    <Input
                      id="name"
                      placeholder="Nama tipe report"
                      value={data.name}
                      onChange={(e) => handleChange('name', e.target.value)}
                      className={errors.name ? "border-destructive" : ""}
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Deskripsi</Label>
                    <Textarea
                      id="description"
                      placeholder="Deskripsi tipe report"
                      value={data.description}
                      onChange={(e) => handleChange('description', e.target.value)}
                      className={errors.description ? "border-destructive" : ""}
                    />
                    {errors.description && (
                      <p className="text-sm text-destructive">{errors.description}</p>
                    )}
                  </div>

                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle>Definisi Field</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="space-y-2">
                          <Label htmlFor="fieldName">Nama Field</Label>
                          <Input
                            id="fieldName"
                            placeholder="Contoh: account_number"
                            value={fieldName}
                            onChange={(e) => setFieldName(e.target.value)}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="fieldPosition">Posisi</Label>
                          <Input
                            id="fieldPosition"
                            type="number"
                            placeholder="Contoh: 1"
                            value={fieldPosition}
                            onChange={(e) => setFieldPosition(e.target.value)}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="fieldType">Tipe Data</Label>
                          <Select value={fieldType} onValueChange={setFieldType}>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih tipe data" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="string">String</SelectItem>
                              <SelectItem value="integer">Integer</SelectItem>
                              <SelectItem value="decimal">Decimal</SelectItem>
                              <SelectItem value="date">Date</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleAddField}
                          className="flex items-center gap-2"
                        >
                          <Plus className="h-4 w-4" /> Tambah Field
                        </Button>
                      </div>

                      {fields.length > 0 && (
                        <div className="mt-4" id='fields-container'>
                          <DndContext
                            sensors={sensors}
                            collisionDetection={closestCenter}
                            onDragEnd={handleDragEnd}
                          >
                            <SortableContext
                              items={fields.map(field => field.position.toString())}
                              strategy={verticalListSortingStrategy}
                            >
                              {fields.map((field, index) => (
                                <SortableFieldItem
                                  key={`${field.name}-${field.position}`}
                                  field={field}
                                  index={index}
                                  onRemove={handleRemoveField}
                                />
                              ))}
                            </SortableContext>
                          </DndContext>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                <div className="flex justify-end mt-6">
                  <Button
                    type="submit"
                    disabled={processing}
                  >
                    {processing ? "Menyimpan..." : "Simpan Perubahan"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}