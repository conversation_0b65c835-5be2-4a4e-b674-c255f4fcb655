import AppLogoIcon from '@/components/app-logo-icon';
import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="bg-background flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
            <div className="w-full max-w-sm">
                <div className="flex flex-col gap-8">
                    <div className="flex flex-col items-center gap-4">
                        <Link href={route('home')} className="flex flex-col items-center gap-2 font-medium">
                            
                            <div className="mb-2">
                                <img src="/assets/images/pdsb_text_logo.png" alt="PDSB Logo" className="h-8 w-auto" />
                            </div>
                            <div className="mb-1 flex items-center mx-auto justify-center font-extrabold text-xl">
                                {/* <AppLogoIcon className="h-12 w-auto" /> */}
                                {"Centralized SLIK Reporting"}
                            </div>
                            <span className="sr-only">{title}</span>
                        </Link>

                        {/* <div className="space-y-2 text-center">
                            <h1 className="text-xl font-medium">{title}</h1>
                            <p className="text-muted-foreground text-center text-sm">{description}</p>
                        </div> */}
                    </div>
                    {children}
                </div>
            </div>
        </div>
    );
}
