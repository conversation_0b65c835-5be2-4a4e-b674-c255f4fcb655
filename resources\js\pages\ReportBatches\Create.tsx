import { Head, useForm, router } from '@inertiajs/react';
import { useState } from 'react';
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Loader2,
  Calendar,
  FileText
} from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';

import { 
  ReportBatchFormData, 
  ValidationResults, 
  PeriodSuggestion,
  MONTH_NAMES 
} from '@/types/report';

interface Props {
  reportCodes: string[];
  currentPeriod: PeriodSuggestion;
  institutionCode: string;
  branchCode: string;
}

type Step = 'form' | 'validation' | 'generation';

export default function CreateReportBatch({ 
  reportCodes, 
  currentPeriod, 
  institutionCode, 
  branchCode 
}: Props) {
  const [currentStep, setCurrentStep] = useState<Step>('form');
  const [validationResults, setValidationResults] = useState<ValidationResults | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  const { data, setData, post, processing, errors, reset } = useForm<ReportBatchFormData>({
    institution_code: institutionCode,
    branch_code: branchCode,
    report_code: '',
    report_month: currentPeriod.month,
    report_year: currentPeriod.year,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (currentStep === 'form') {
      // Create the report batch first
      post(route('report.store'), {
        onSuccess: (page) => {
          const reportBatch = page.props.data as { id: number };
          toast.success('Report batch created successfully.');
          setCurrentStep('validation');
          // Start validation automatically
          validateData(reportBatch.id);
        },
        onError: () => {
          toast.error('Failed to create report batch.');
        },
      });
    }
  };

  const validateData = async (reportBatchId: number) => {
    setIsValidating(true);
    
    try {
      const response = await fetch(route('report.validate-data', reportBatchId), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
      });

      const result = await response.json();
      
      if (result.success) {
        setValidationResults(result.validation_results);
        toast.success('Data validation completed.');
      } else {
        toast.error('Validation failed: ' + result.message);
      }
    } catch (error) {
      toast.error('Failed to validate data.');
    } finally {
      setIsValidating(false);
    }
  };

  const generateData = async (reportBatchId: number) => {
    if (!validationResults?.valid) {
      toast.error('Cannot generate data. Please resolve validation errors first.');
      return;
    }

    setIsGenerating(true);
    setCurrentStep('generation');
    
    // Simulate progress
    const progressInterval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return prev;
        }
        return prev + Math.random() * 10;
      });
    }, 500);

    try {
      const response = await fetch(route('report.generate-data', reportBatchId), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
      });

      const result = await response.json();
      
      clearInterval(progressInterval);
      setGenerationProgress(100);
      
      if (result.success) {
        toast.success(`Data generation completed. ${result.record_count} records generated.`);
        setTimeout(() => {
          router.visit(route('report.show', reportBatchId));
        }, 2000);
      } else {
        toast.error('Generation failed: ' + result.error);
        setIsGenerating(false);
        setCurrentStep('validation');
      }
    } catch (error) {
      clearInterval(progressInterval);
      toast.error('Failed to generate data.');
      setIsGenerating(false);
      setCurrentStep('validation');
    }
  };

  const getStepIcon = (step: Step) => {
    switch (step) {
      case 'form':
        return <FileText className="h-5 w-5" />;
      case 'validation':
        return <CheckCircle className="h-5 w-5" />;
      case 'generation':
        return <Loader2 className="h-5 w-5" />;
    }
  };

  const getStepTitle = (step: Step) => {
    switch (step) {
      case 'form':
        return 'Report Configuration';
      case 'validation':
        return 'Data Validation';
      case 'generation':
        return 'Data Generation';
    }
  };

  const renderValidationResults = () => {
    if (!validationResults) return null;

    return (
      <div className="space-y-4">
        {/* Overall Status */}
        <Alert className={validationResults.valid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {validationResults.valid ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <XCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={validationResults.valid ? 'text-green-800' : 'text-red-800'}>
            {validationResults.valid 
              ? 'Data validation passed. Ready for generation.' 
              : 'Data validation failed. Please resolve issues before proceeding.'
            }
          </AlertDescription>
        </Alert>

        {/* Previous Period Check */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <span className="text-sm font-medium">Previous Period Data</span>
          <Badge variant={validationResults.previous_period_available ? 'default' : 'secondary'}>
            {validationResults.previous_period_available ? 'Available' : 'Not Available'}
          </Badge>
        </div>

        {/* Comparison Period Check */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <span className="text-sm font-medium">Comparison Period Data</span>
          <Badge variant={validationResults.comparison_period_available ? 'default' : 'destructive'}>
            {validationResults.comparison_period_available ? 'Available' : 'Missing'}
          </Badge>
        </div>

        {/* Warnings */}
        {validationResults.warnings.length > 0 && (
          <Alert className="border-yellow-200 bg-yellow-50">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-yellow-800">
              <div className="font-medium mb-2">Warnings:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationResults.warnings.map((warning, index) => (
                  <li key={index} className="text-sm">{warning}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Errors */}
        {validationResults.errors.length > 0 && (
          <Alert className="border-red-200 bg-red-50">
            <XCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <div className="font-medium mb-2">Errors:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationResults.errors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  return (
    <AppLayout>
      <Head title="Create Report Batch" />

      <div className="max-w-4xl mx-auto space-y-6  md:px-2">
        {/* Header */}
        <div className="flex items-center justify-between gap-4">
          <Button variant="ghost" onClick={() => router.visit(route('report.index'))}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Report Batches
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Report</h1>
            {/* <p className="text-muted-foreground">
              Generate a new SLIK report batch for {currentPeriod.period_name}
            </p> */}
          </div>
        </div>

        {/* Progress Steps */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              {(['form', 'validation', 'generation'] as Step[]).map((step, index) => (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep === step 
                      ? 'border-blue-500 bg-blue-500 text-white' 
                      : index < (['form', 'validation', 'generation'] as Step[]).indexOf(currentStep)
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-gray-300 bg-gray-100 text-gray-500'
                  }`}>
                    {getStepIcon(step)}
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium">{getStepTitle(step)}</div>
                  </div>
                  {index < 2 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      index < (['form', 'validation', 'generation'] as Step[]).indexOf(currentStep)
                        ? 'bg-green-500'
                        : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        {currentStep === 'form' && (
          <Card>
            <CardHeader>
              <CardTitle>Report Configuration</CardTitle>
              <CardDescription>
                Configure the report batch parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="institution_code">Institution Code</Label>
                    <Input
                      id="institution_code"
                      value={data.institution_code}
                      onChange={(e) => setData('institution_code', e.target.value)}
                      disabled
                    />
                    {errors.institution_code && (
                      <p className="text-sm text-red-600">{errors.institution_code}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="branch_code">Branch Code</Label>
                    <Input
                      id="branch_code"
                      value={data.branch_code}
                      onChange={(e) => setData('branch_code', e.target.value)}
                      disabled
                    />
                    {errors.branch_code && (
                      <p className="text-sm text-red-600">{errors.branch_code}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="report_code">Report Code</Label>
                    <Select
                      value={data.report_code}
                      onValueChange={(value) => setData('report_code', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select report code" />
                      </SelectTrigger>
                      <SelectContent>
                        {reportCodes.map(code => (
                          <SelectItem key={code} value={code}>{code}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.report_code && (
                      <p className="text-sm text-red-600">{errors.report_code}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="report_year">Report Year</Label>
                    <Input
                      id="report_year"
                      type="number"
                      value={data.report_year}
                      onChange={(e) => setData('report_year', parseInt(e.target.value))}
                      min="2020"
                      max={new Date().getFullYear()}
                    />
                    {errors.report_year && (
                      <p className="text-sm text-red-600">{errors.report_year}</p>
                    )}
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="report_month">Report Month</Label>
                    <Select
                      value={data.report_month.toString()}
                      onValueChange={(value) => setData('report_month', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {MONTH_NAMES.map((month, index) => (
                          <SelectItem key={index + 1} value={(index + 1).toString()}>
                            {month}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.report_month && (
                      <p className="text-sm text-red-600">{errors.report_month}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={processing}>
                    {processing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        Next: Validate Data
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {currentStep === 'validation' && (
          <Card>
            <CardHeader>
              <CardTitle>Data Validation</CardTitle>
              <CardDescription>
                Checking data availability and requirements
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isValidating ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mr-3" />
                  <span>Validating data availability...</span>
                </div>
              ) : (
                <div className="space-y-6">
                  {renderValidationResults()}
                  
                  {validationResults && (
                    <div className="flex justify-end">
                      <Button
                        onClick={() => generateData(1)} // You'll need to pass the actual report batch ID
                        disabled={!validationResults.valid}
                      >
                        {validationResults.valid ? (
                          <>
                            Generate Data
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </>
                        ) : (
                          'Resolve Issues First'
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {currentStep === 'generation' && (
          <Card>
            <CardHeader>
              <CardTitle>Data Generation</CardTitle>
              <CardDescription>
                Generating report data for the selected period
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(generationProgress)}%</span>
                  </div>
                  <Progress value={generationProgress} className="w-full" />
                </div>

                {isGenerating ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin mr-3" />
                    <span>Generating report data...</span>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Generation Complete!</h3>
                    <p className="text-muted-foreground">
                      Report data has been successfully generated.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
