import { useState, useRef } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { toast } from 'sonner';

import { ReportType } from '@/types/reports';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Form, FormField } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon } from 'lucide-react';
import { format } from "date-fns"
import { Calendar } from '@/components/ui/calendar';
import { MonthPicker } from '@/components/ui/monthpicker';


interface CreateProps {
  reportTypes: ReportType[];
}

export default function ReportCreate({ reportTypes }: CreateProps) {
  const { data, setData, post, processing, errors } = useForm({
    periode: undefined as Date | undefined,
    report_type_id: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    post(route('reports.store'), {
      forceFormData: true,
    });
  };

  const handleChange = (name: string, value: any) => {
    setData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  return (
    <AppLayout>
      <Head title="Create Report" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6 bg-white border-b border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold">Tambah Hybrid Report Baru</h2>
            <Link href={route('reports.index')}>
              <Button variant="outline">Kembali ke Daftar</Button>
            </Link>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Create</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-sm text-muted-foreground">

              </p>

              {/* <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="report_file">File Report</Label>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Input
                        id="report_file"
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        accept=".txt,.csv"
                        className="hidden"
                      />

                      <div
                        className="border-2 border-dashed rounded-md p-8 text-center cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <div className="flex flex-col items-center gap-2">
                          <Upload className="h-10 w-10 text-muted-foreground" />
                          <p className="text-sm font-medium">
                            Klik untuk memilih file
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Hanya mendukung file .txt atau .csv
                          </p>
                          {fileName && (
                            <p className="text-sm font-semibold mt-2 text-primary">
                              File terpilih: {fileName}
                            </p>
                          )}
                        </div>
                      </div>

                      {errors.report_file && (
                        <p className="text-sm text-destructive">{errors.report_file}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={processing || !data.report_file}
                      className="flex items-center gap-2"
                    >
                      {processing ? "Memproses..." : "Upload dan Proses"}
                    </Button>
                  </div>
                </form> */}


              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-4">
                    <Label>Periode Report</Label>
                    <div className='w-full'>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant={"outline"} className={cn("w-[280px] justify-start text-left font-normal", !data.periode && "text-muted-foreground")}>
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {data.periode ? format(data.periode, "MMM yyyy") : <span>Pick a month</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <MonthPicker onMonthSelect={(periode) => handleChange('periode', periode)} selectedMonth={data.periode} />
                        </PopoverContent>
                      </Popover>
                      {/* <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-[240px] justify-start text-left font-normal",
                              !data.periode && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon />
                            {data.periode ? format(data.periode, "PPP") : <span>Pick a date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={data.periode ? new Date(data.periode) : undefined}
                            onSelect={(periode) => handleChange('periode', periode)}
                            initialFocus

                          />
                        </PopoverContent>
                      </Popover> */}
                    </div>

                    {errors.periode && (
                      <p className="text-sm text-destructive">{errors.periode}</p>
                    )}
                  </div>

                  <div className="space-y-4">
                    <Label htmlFor="name">Report Type</Label>
                    <Select
                      onValueChange={(value) => handleChange('report_type_id', value)}
                      defaultValue={data.report_type_id}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih tipe report" />
                      </SelectTrigger>
                      <SelectContent>
                        {reportTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.id} - {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.report_type_id && (
                      <p className="text-sm text-destructive">{errors.report_type_id}</p>
                    )}
                  </div>

                  {/* <div className="space-y-2">
                      <Label htmlFor="description">Deskripsi</Label>
                      <textarea
                        id="description"
                        placeholder="Deskripsi tipe report"
                        value={data.description}
                        onChange={(e) => handleChange('description', e.target.value)}
                        className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${errors.description ? "border-destructive" : ""}`}
                      />
                      {errors.description && (
                        <p className="text-sm text-destructive">{errors.description}</p>
                      )}
                    </div> */}
                </div>

                <div className="flex justify-end mt-6">
                  <Button
                    type="submit"
                    disabled={processing}
                  >
                    {processing ? "Menyimpan..." : "Simpan"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
