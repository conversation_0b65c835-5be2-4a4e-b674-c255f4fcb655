import { Head, Link, router } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import {
    FilePlus,
    Search,
    X,
    ChevronDown,
    MoreHorizontal,
    Pencil,
    Trash2,
    ArrowUpDown,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from '@/components/ui/pagination';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Elements',
        href: '/elements',
    },
];

interface ReportElement {
    id: number;
    label: string;
    field: string;
    type: string;
    template_details_count: number;
    created_at: string;
    updated_at: string;
}

interface ElementsIndexProps {
    elements: {
        data: ReportElement[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    filters?: {
        search?: string;
        type?: string;
    };
}

// Element types for filtering
const elementTypes = [
    { value: 'string', label: 'String' },
    { value: 'char', label: 'Character' },
    { value: 'date', label: 'Date' },
    { value: 'number', label: 'Number' },
    { value: 'boolean', label: 'Boolean' },
];

export default function ElementsIndex({ elements, filters = {} }: ElementsIndexProps) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [typeFilter, setTypeFilter] = useState(filters.type || '');
    const [isTypeOpen, setIsTypeOpen] = useState(false);

    // Handle search input
    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        applyFilters();
    };

    // Apply filters and update URL
    const applyFilters = () => {
        router.get(
            route('elements.index'),
            {
                search: searchQuery || undefined,
                type: typeFilter || undefined
            },
            {
                preserveState: true,
                replace: true
            }
        );
    };

    // Clear all filters
    const clearFilters = () => {
        setSearchQuery('');
        setTypeFilter('');
        router.get(
            route('elements.index'),
            {},
            {
                preserveState: true,
                replace: true
            }
        );
    };

    // Apply type filter when it changes
    useEffect(() => {
        if (typeFilter !== (filters.type || '')) {
            applyFilters();
        }
    }, [typeFilter]);

    // Handle pagination
    const goToPage = (page: number) => {
        router.get(
            route('elements.index', {
                page,
                search: searchQuery || undefined,
                type: typeFilter || undefined
            }),
            {},
            {
                preserveState: true,
                replace: true
            }
        );
    };

    // Generate pagination items
    const renderPaginationItems = () => {
        const items = [];
        const { current_page, last_page } = elements;

        // Previous button
        items.push(
            <PaginationItem key="prev">
                <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                        e.preventDefault();
                        if (current_page > 1) goToPage(current_page - 1);
                    }}
                    className={current_page === 1 ? 'pointer-events-none opacity-50' : ''}
                />
            </PaginationItem>
        );

        // First page
        if (current_page > 3) {
            items.push(
                <PaginationItem key="1">
                    <PaginationLink
                        href="#"
                        onClick={(e) => {
                            e.preventDefault();
                            goToPage(1);
                        }}
                    >
                        1
                    </PaginationLink>
                </PaginationItem>
            );
        }

        // Ellipsis if needed
        if (current_page > 4) {
            items.push(
                <PaginationItem key="ellipsis1">
                    <PaginationEllipsis />
                </PaginationItem>
            );
        }

        // Pages around current page
        for (let i = Math.max(1, current_page - 2); i <= Math.min(last_page, current_page + 2); i++) {
            items.push(
                <PaginationItem key={i}>
                    <PaginationLink
                        href="#"
                        onClick={(e) => {
                            e.preventDefault();
                            goToPage(i);
                        }}
                        isActive={i === current_page}
                    >
                        {i}
                    </PaginationLink>
                </PaginationItem>
            );
        }

        // Ellipsis if needed
        if (current_page < last_page - 3) {
            items.push(
                <PaginationItem key="ellipsis2">
                    <PaginationEllipsis />
                </PaginationItem>
            );
        }

        // Last page
        if (current_page < last_page - 2 && last_page > 1) {
            items.push(
                <PaginationItem key={last_page}>
                    <PaginationLink
                        href="#"
                        onClick={(e) => {
                            e.preventDefault();
                            goToPage(last_page);
                        }}
                    >
                        {last_page}
                    </PaginationLink>
                </PaginationItem>
            );
        }

        // Next button
        items.push(
            <PaginationItem key="next">
                <PaginationNext
                    href="#"
                    onClick={(e) => {
                        e.preventDefault();
                        if (current_page < last_page) goToPage(current_page + 1);
                    }}
                    className={current_page === last_page ? 'pointer-events-none opacity-50' : ''}
                />
            </PaginationItem>
        );

        return items;
    };

    // Handle element deletion
    const handleDelete = (id: number) => {
        if (confirm('Are you sure you want to delete this element?')) {
            router.delete(route('elements.destroy', { element: id }), {
                onSuccess: () => {
                    toast.success('Element deleted successfully');
                },
                onError: () => {
                    toast.error('Failed to delete element');
                }
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Report Elements" />

            <div className="flex flex-col gap-6 p-4">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-2xl font-bold">Report Elements</h1>
                        <div className="mt-1">
                            <Link
                                href={route('elements.data-table')}
                                className="text-sm text-blue-600 hover:underline"
                            >
                                View with Data Table
                            </Link>
                        </div>
                    </div>
                    <Button asChild>
                        <Link href={route('elements.create')}>
                            <FilePlus className="mr-2 h-4 w-4" />
                            Create Element
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-1">
                        <form onSubmit={handleSearch} className="flex w-full items-center space-x-2">
                            <div className="relative flex-1">
                                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="texxt"
                                    placeholder="Search elements..."
                                    className="pl-8"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                />
                                {searchQuery && (
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-3"
                                        onClick={() => {
                                            setSearchQuery('');
                                            if (filters.search) applyFilters();
                                        }}
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                )}
                            </div>
                            <Button type="submit">Search</Button>
                        </form>
                    </div>

                    <div className="flex gap-2">
                        <Popover open={isTypeOpen} onOpenChange={setIsTypeOpen}>
                            <PopoverTrigger asChild>
                                <Button variant="outline" className="w-[180px] justify-between">
                                    {typeFilter ? elementTypes.find(t => t.value === typeFilter)?.label : "Filter by Type"}
                                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[180px] p-0">
                                <Command>
                                    <CommandInput placeholder="Search type..." />
                                    <CommandList>
                                        <CommandEmpty>No type found.</CommandEmpty>
                                        <CommandGroup>
                                            <CommandItem
                                                onSelect={() => {
                                                    setTypeFilter('');
                                                    setIsTypeOpen(false);
                                                }}
                                                className="justify-between"
                                            >
                                                All Types
                                                {!typeFilter && <Check className="h-4 w-4" />}
                                            </CommandItem>
                                            {elementTypes.map((type) => (
                                                <CommandItem
                                                    key={type.value}
                                                    onSelect={() => {
                                                        setTypeFilter(type.value);
                                                        setIsTypeOpen(false);
                                                    }}
                                                    className="justify-between"
                                                >
                                                    {type.label}
                                                    {typeFilter === type.value && <Check className="h-4 w-4" />}
                                                </CommandItem>
                                            ))}
                                        </CommandGroup>
                                    </CommandList>
                                </Command>
                            </PopoverContent>
                        </Popover>

                        {(searchQuery || typeFilter) && (
                            <Button variant="ghost" onClick={clearFilters}>
                                Clear Filters
                            </Button>
                        )}
                    </div>
                </div>

                {/* Active filters */}
                {(searchQuery || typeFilter) && (
                    <div className="flex flex-wrap items-center gap-2 rounded-md border border-border bg-muted/40 px-3 py-2">
                        <span className="text-sm font-medium">Active filters:</span>
                        {searchQuery && (
                            <Badge variant="secondary" className="flex items-center gap-1">
                                Search: {searchQuery}
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-4 w-4 p-0 ml-1"
                                    onClick={() => {
                                        setSearchQuery('');
                                        applyFilters();
                                    }}
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </Badge>
                        )}
                        {typeFilter && (
                            <Badge variant="secondary" className="flex items-center gap-1">
                                Type: {elementTypes.find(t => t.value === typeFilter)?.label}
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-4 w-4 p-0 ml-1"
                                    onClick={() => {
                                        setTypeFilter('');
                                        applyFilters();
                                    }}
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </Badge>
                        )}
                    </div>
                )}

                {/* Elements Table */}
                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[50px]">ID</TableHead>
                                <TableHead>Label</TableHead>
                                <TableHead>Field</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead className="text-center">Used In</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {elements.data.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="h-24 text-center">
                                        No elements found.
                                    </TableCell>
                                </TableRow>
                            ) : (
                                elements.data.map((element) => (
                                    <TableRow key={element.id}>
                                        <TableCell className="font-medium">{element.id}</TableCell>
                                        <TableCell>{element.label}</TableCell>
                                        <TableCell className="font-mono text-sm">{element.field}</TableCell>
                                        <TableCell>
                                            <Badge variant="outline">
                                                {element.type}
                                            </Badge>
                                        </TableCell>
                                        <TableCell className="text-center">
                                            {element.template_details_count > 0 ? (
                                                <Badge variant="secondary">
                                                    {element.template_details_count} template{element.template_details_count !== 1 ? 's' : ''}
                                                </Badge>
                                            ) : (
                                                <span className="text-muted-foreground text-sm">Not used</span>
                                            )}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                        <span className="sr-only">Open menu</span>
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem asChild>
                                                        <Link href={route('elements.show', { element: element.id })}>
                                                            View details
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem asChild>
                                                        <Link href={route('elements.edit', { element: element.id })}>
                                                            <Pencil className="mr-2 h-4 w-4" />
                                                            Edit
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => handleDelete(element.id)}
                                                        className="text-destructive focus:text-destructive"
                                                        disabled={element.template_details_count > 0}
                                                    >
                                                        <Trash2 className="mr-2 h-4 w-4" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Pagination */}
                {elements.last_page > 1 && (
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                            Showing <span className="font-medium">{elements.from}</span> to{" "}
                            <span className="font-medium">{elements.to}</span> of{" "}
                            <span className="font-medium">{elements.total}</span> elements
                        </div>
                        <Pagination>
                            <PaginationContent>
                                {renderPaginationItems()}
                            </PaginationContent>
                        </Pagination>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}

// Check icon component for the filter dropdown
function Check(props: React.SVGProps<SVGSVGElement>) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            {...props}
        >
            <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M4.5 12.75l6 6 9-13.5"
            />
        </svg>
    );
}
