# Central SLIK Reporting


System will snapshot data everyday, on day - 1 
- Prefer build F01 Report first, after 
- 


F01 Final Bulan Lalu, Query dan Compare dengan File Periode Bulan Lalu :

Database Penting DWH :

- KRP_LBUT query by 


Refactor :
1. Make CronJob daily to snapshooting data KRP_LBUT
2. Do flaging for 00 and 01, <PERSON><PERSON> dan <PERSON>, <PERSON>ka periode lalu 

## Purpose
**Automation, centralization, efficiency, and standardization of SLIK report data generation and ## Key Features
- **Automated Data Collection**: Streamlines the gathering of financial data from various sources
- **Centralized Processing**: Consolidates all reporting operations into a single platform
- **Standardized Formatting**: Ensures all reports meet OJK's SLIK specifications
- **Validation Engine**: Performs comprehensive checks to ensure data accuracy and compliance
- **Secure Transmission**: Facilitates secure submission of reports to the regulatory authorities
- **Audit Trail**: Maintains detailed logs of all report generation and submission activities
- **User Role Management**: Provides controlled access based on user responsibilities
- **Reporting Dashboard**: Offers insights into submission history and reporting statistics

## System Architecture
The application follows a modern Laravel architecture with:
- **MVC Pattern**: Clear separation of concerns between models, views, and controllers
- **RESTful API Services**: For data integration with other systems
- **Queue Workers**: For handling resource-intensive report generation tasks
- **Scheduled Jobs**: For automated report generation and submission

## Requirements
- PHP 8.1 or higher
- Composer 2.0 or higher
- Node.js v14+ & NPM v6+
- MySQL 8.0 or PostgreSQL 13+ database
- Redis (optional, for queues and caching)
- Laravel requirements:
  - BCMath PHP Extension
  - Ctype PHP Extension
  - Fileinfo PHP Extension
  - JSON PHP Extension
  - Mbstring PHP Extension
  - OpenSSL PHP Extension
  - PDO PHP Extension
  - Tokenizer PHP Extension
  - XML PHP Extension
  - GD Library (for report generation)
## Installation

### Development Environment Setup

1. Clone the repository
   ```bash
   git clone [repository-url]
   cd central-slik-reporting
   ```

2. Install PHP dependencies
   ```bash
   composer install
   ```
   This installs all the required PHP packages defined in the composer.json file, including the Laravel framework and its dependencies.

3. Install JavaScript dependencies
   ```bash
   npm install
   ```
   This step sets up the frontend tooling including Vite, Tailwind CSS, and any JavaScript libraries used in the project.

4. Copy environment file and set up configuration
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```
   The key:generate command creates a secure application key that is used for encryption purposes within the application.

5. Configure your database in the `.env` file
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=central_slik
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```
   Also configure any other necessary services like Redis, mail, etc.

6. Run migrations and seed the database
   ```bash
   php artisan migrate --seed
   ```
   This creates all the required database tables and populates them with initial data where needed.

7. Build assets for development
   ```bash
   npm run dev
   ```
   For production environments, use:
   ```bash
   npm run build
   ```

### Additional Configuration

1. Set up scheduled tasks (for production)
   ```bash
   # Add to server crontab
   * * * * * cd /path-to-project && php artisan schedule:run >> /dev/null 2>&1
   ```

2. Configure queue workers (if using Redis for queues)
   ```bash
   # Set up supervisor or systemd to run
   php artisan queue:work --queue=reports,default
   ```
## Basic Usage

### Development Server

1. Start the development server
   ```bash
   php artisan serve
   ```
   This launches a development server at http://localhost:8000

2. For frontend development with Vite (hot reload)
   ```bash
   npm run dev
   ```
   This enables real-time compilation and hot module replacement for frontend assets.

3. Access the application in your browser
   ```
   http://localhost:8000
   ```

### Application Workflow

1. **User Authentication**: Log in with appropriate credentials based on your role
2. **Data Import**: Upload or connect to data sources for SLIK report generation
3. **Data Validation**: Review validation results and fix any identified issues
4. **Report Generation**: Generate SLIK-compliant reports in the required format
5. **Submission**: Submit reports to OJK through the appropriate channels
6. **Audit & Analysis**: Review submission history and reporting analytics

## Project Structure

```
central-slik-reporting/
├── app/                    # Application core code
│   ├── Console/           # Artisan commands
│   ├── Http/              # Controllers, Middleware, Requests
│   ├── Models/            # Eloquent models
│   ├── Services/          # Business logic services
│   └── Reporting/         # SLIK report generation logic
├── config/                # Configuration files
├── database/              # Migrations and seeders
├── public/                # Publicly accessible files
├── resources/             # Views, frontend assets
│   ├── js/               # JavaScript files
│   ├── css/              # Stylesheets
│   └── views/            # Blade templates
├── routes/                # Route definitions
├── storage/               # Application storage
└── tests/                 # Automated tests
```

## Common Commands

### Database Operations
- Run migrations: `php artisan migrate`
- Reset database and run all migrations: `php artisan migrate:fresh --seed`
- Create a new migration: `php artisan make:migration create_table_name`

### Code Generation
- Create a controller: `php artisan make:controller ControllerName`
- Create a model with migration: `php artisan make:model ModelName -m`
- Create a service class: `php artisan make:class Services/ServiceName`
- Create a policy: `php artisan make:policy ModelPolicy`

### Testing and Maintenance
- Run tests: `php artisan test`
- Clear cache: `php artisan cache:clear`
- Clear config cache: `php artisan config:clear`
- Clear route cache: `php artisan route:clear`
- Clear view cache: `php artisan view:clear`
- Clear application cache: `php artisan optimize:clear`

### SLIK Report Operations
- Generate report: `php artisan slik:generate --month=01 --year=2025`
- Validate report: `php artisan slik:validate {report_id}`
- Submit report: `php artisan slik:submit {report_id}`

### User Management
- Create a new user: `php artisan user:create`
- Create a new user with options: `php artisan user:create --name="Admin" --email="<EMAIL>" --password="secure_password" --verify`
