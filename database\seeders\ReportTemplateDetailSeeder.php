<?php

namespace Database\Seeders;

use App\Models\ReportElement;
use App\Models\ReportTemplate;
use App\Models\ReportTemplateDetail;
use Illuminate\Database\Seeder;

class ReportTemplateDetailSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing template details to avoid duplicates
        ReportTemplateDetail::truncate();

        // Define field orders for each template
        $templateFieldOrders = [
            'D01' => [
                'flag_detail',
                'customer_no',
                'legal_doc_name',
                'legal_id',
                'name_1',
                'name_2',
                'gender_code',
                'gender',
                'place_of_birth',
                'date_of_birth',
                'cus_npwp',
                'address',
                'ktp_kelurahan',
                'ktp_kecamatan',
                'sid_dati2debtor',
                'post_code',
                'phone_1',
                'sms_1',
                'email_1',
                'nationality',
                'occupation',
                'employers_name',
                'sid_jenis_usaha',
                'employers_add',
                'last_education',
                'kyc_incom_rng',
                'kyc_income_src',
                'no_of_dependents',
                'sid_hub_bank',
                'lbu_gol_deb',
                'marital_status',
                'spouse_id',
                'spouse_name',
                'spou_dt_of_birt',
                'l_pisah_harta',
                'sid_melanggar',
                'sid_melampaui',
                'mother_maid_nam',
                'branch_code',
                'data_operation',
            ],
            // Untuk template lain, kita gunakan field yang sama untuk sementara
            // Ini bisa diupdate nanti sesuai dengan spesifikasi masing-masing laporan
            'D02' => [
                'flag_detail',
                'customer_no',
                'legal_doc_name',
                'legal_id',
                'name_1',
                'name_2',
                'gender_code',
                'gender',
                'place_of_birth',
                'date_of_birth',
                'cus_npwp',
                'address',
                'ktp_kelurahan',
                'ktp_kecamatan',
                'sid_dati2debtor',
                'post_code',
                'phone_1',
                'sms_1',
                'email_1',
                'nationality',
                'occupation',
                'employers_name',
                'sid_jenis_usaha',
                'employers_add',
                'last_education',
                'kyc_incom_rng',
                'kyc_income_src',
                'no_of_dependents',
                'sid_hub_bank',
                'lbu_gol_deb',
                'marital_status',
                'spouse_id',
                'spouse_name',
                'spou_dt_of_birt',
                'l_pisah_harta',
                'sid_melanggar',
                'sid_melampaui',
                'mother_maid_nam',
                'branch_code',
                'data_operation',
            ],
            'F01' => [
                'flag_detail',
                'customer_no',
                'legal_doc_name',
                'legal_id',
                'name_1',
                'name_2',
                'gender_code',
                'gender',
                'place_of_birth',
                'date_of_birth',
                'cus_npwp',
                'address',
                'ktp_kelurahan',
                'ktp_kecamatan',
                'sid_dati2debtor',
                'post_code',
                'phone_1',
                'sms_1',
                'email_1',
                'nationality',
                'occupation',
                'employers_name',
                'sid_jenis_usaha',
                'employers_add',
                'last_education',
                'kyc_incom_rng',
                'kyc_income_src',
                'no_of_dependents',
                'sid_hub_bank',
                'lbu_gol_deb',
                'marital_status',
                'spouse_id',
                'spouse_name',
                'spou_dt_of_birt',
                'l_pisah_harta',
                'sid_melanggar',
                'sid_melampaui',
                'mother_maid_nam',
                'branch_code',
                'data_operation',
            ],
            'F03' => [
                'flag_detail',
                'customer_no',
                'legal_doc_name',
                'legal_id',
                'name_1',
                'name_2',
                'gender_code',
                'gender',
                'place_of_birth',
                'date_of_birth',
                'cus_npwp',
                'address',
                'ktp_kelurahan',
                'ktp_kecamatan',
                'sid_dati2debtor',
                'post_code',
                'phone_1',
                'sms_1',
                'email_1',
                'nationality',
                'occupation',
                'employers_name',
                'sid_jenis_usaha',
                'employers_add',
                'last_education',
                'kyc_incom_rng',
                'kyc_income_src',
                'no_of_dependents',
                'sid_hub_bank',
                'lbu_gol_deb',
                'marital_status',
                'spouse_id',
                'spouse_name',
                'spou_dt_of_birt',
                'l_pisah_harta',
                'sid_melanggar',
                'sid_melampaui',
                'mother_maid_nam',
                'branch_code',
                'data_operation',
            ],
            'F05' => [
                'flag_detail',
                'customer_no',
                'legal_doc_name',
                'legal_id',
                'name_1',
                'name_2',
                'gender_code',
                'gender',
                'place_of_birth',
                'date_of_birth',
                'cus_npwp',
                'address',
                'ktp_kelurahan',
                'ktp_kecamatan',
                'sid_dati2debtor',
                'post_code',
                'phone_1',
                'sms_1',
                'email_1',
                'nationality',
                'occupation',
                'employers_name',
                'sid_jenis_usaha',
                'employers_add',
                'last_education',
                'kyc_incom_rng',
                'kyc_income_src',
                'no_of_dependents',
                'sid_hub_bank',
                'lbu_gol_deb',
                'marital_status',
                'spouse_id',
                'spouse_name',
                'spou_dt_of_birt',
                'l_pisah_harta',
                'sid_melanggar',
                'sid_melampaui',
                'mother_maid_nam',
                'branch_code',
                'data_operation',
            ],
            'P01' => [
                'flag_detail',
                'customer_no',
                'legal_doc_name',
                'legal_id',
                'name_1',
                'name_2',
                'gender_code',
                'gender',
                'place_of_birth',
                'date_of_birth',
                'cus_npwp',
                'address',
                'ktp_kelurahan',
                'ktp_kecamatan',
                'sid_dati2debtor',
                'post_code',
                'phone_1',
                'sms_1',
                'email_1',
                'nationality',
                'occupation',
                'employers_name',
                'sid_jenis_usaha',
                'employers_add',
                'last_education',
                'kyc_incom_rng',
                'kyc_income_src',
                'no_of_dependents',
                'sid_hub_bank',
                'lbu_gol_deb',
                'marital_status',
                'spouse_id',
                'spouse_name',
                'spou_dt_of_birt',
                'l_pisah_harta',
                'sid_melanggar',
                'sid_melampaui',
                'mother_maid_nam',
                'branch_code',
                'data_operation',
            ],
        ];

        // Create template details for each template
        foreach ($templateFieldOrders as $templateAlias => $fieldOrder) {
            $template = ReportTemplate::where('alias', $templateAlias)->first();

            if (! $template) {
                continue;
            }

            foreach ($fieldOrder as $order => $field) {
                $element = ReportElement::where('field', $field)->first();

                if ($element) {
                    ReportTemplateDetail::create([
                        'report_template_id' => $template->id,
                        'report_element_id' => $element->id,
                        'order' => $order + 1,
                    ]);
                }
            }
        }
    }
}
