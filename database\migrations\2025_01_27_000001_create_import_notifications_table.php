<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_notifications', function (Blueprint $table) {
            $table->id();
            $table->string('filename');
            $table->string('report_code', 10);
            $table->unsignedTinyInteger('report_month');
            $table->unsignedSmallInteger('report_year');
            $table->integer('records_imported')->default(0);
            $table->enum('status', ['success', 'failed'])->default('success');
            $table->longText('error_message')->nullable();
            $table->json('import_details')->nullable(); // Additional details like processing time, etc.
            $table->timestamps();

            // Add indexes for performance
            $table->index(['report_code', 'report_year', 'report_month']);
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_notifications');
    }
};
