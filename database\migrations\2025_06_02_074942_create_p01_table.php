<?php

use App\Models\ReportBatch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_p01', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(ReportBatch::class, 'report_batch_id')->constrained()->cascadeOnDelete();
            
            // Basic identification fields
            $table->string('flag_detail')->nullable();
            $table->string('nomor_identitas_penjamin')->nullable();
            $table->string('nomor_rekening_fasilitas')->nullable();
            $table->string('nomor_cif_debitur')->nullable();
            
            // Classification codes
            $table->string('kode_jenis_segmen_fasilitas')->nullable();
            $table->string('kode_jenis_identitas_penjamin')->nullable();
            $table->string('kode_golongan_penjamin')->nullable();
            
            // Guarantor information
            $table->string('nama_penjamin_sesuai_identitas')->nullable();
            $table->string('nama_lengkap_penjamin')->nullable();
            $table->text('alamat_penjamin')->nullable();
            
            // Guarantee details
            $table->decimal('persentase_dijamin', 5, 2)->nullable(); // Percentage with 2 decimal places
            
            // Additional information
            $table->text('keterangan')->nullable();
            $table->string('kode_kantor_cabang')->nullable();
            $table->string('operasi_data')->nullable();
            
            $table->timestamps();
            
            // Add indexes for commonly queried fields
            $table->index('nomor_identitas_penjamin');
            $table->index('nomor_rekening_fasilitas');
            $table->index('nomor_cif_debitur');
            $table->index('kode_kantor_cabang');
            $table->index('kode_jenis_identitas_penjamin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_p01');
    }
};