import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useEffect } from 'react';
import { FileUp, Save } from 'lucide-react';
import { Link } from '@inertiajs/react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import InputError from '@/components/input-error';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Reports',
        href: '/reports',
    },
    {
        title: 'Create Report',
        href: '/reports/create',
    },
];

interface ReportTemplate {
    id: number;
    name: string;
    alias: string;
    description: string;
}

interface Props {
    templates: ReportTemplate[];
    preselectedTemplate?: string;
    preselectedMonth?: number;
    preselectedYear?: number;
}

type ReportForm = {
    name: string;
    report_template_id: string;
    month: string;
    year: string;
    description: string;
    file: File | null;
};

export default function ReportCreate({ templates, preselectedTemplate, preselectedMonth, preselectedYear }: Props) {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // JavaScript months are 0-indexed

    const { data, setData, post, processing, errors } = useForm<ReportForm>({
        name: '',
        report_template_id: preselectedTemplate || '',
        month: preselectedMonth ? preselectedMonth.toString() : currentMonth.toString(),
        year: preselectedYear ? preselectedYear.toString() : currentYear.toString(),
        description: '',
        file: null,
    });

    // Update the report name when template changes
    useEffect(() => {
        if (data.report_template_id) {
            const selectedTemplate = templates.find(t => t.id.toString() === data.report_template_id);
            if (selectedTemplate) {
                const monthName = new Date(parseInt(data.year), parseInt(data.month) - 1, 1).toLocaleString('default', { month: 'long' });
                setData('name', `${selectedTemplate.alias} - ${monthName} ${data.year}`);
            }
        }
    }, [data.report_template_id, data.month, data.year]);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            setData('file', e.target.files[0]);
        }
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('reports.store'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Report" />

            <div className="flex flex-col gap-6 p-4">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold">Create New Report</h1>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Report Information</CardTitle>
                        <CardDescription>
                            Enter the details for the new SLIK report
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-6">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Report Type</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                    />
                                    <InputError message={errors.name} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="template">Report Template</Label>
                                    <Select
                                        value={data.report_template_id}
                                        onValueChange={(value) => setData('report_template_id', value)}
                                    >
                                        <SelectTrigger id="template">
                                            <SelectValue placeholder="Select a template" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {templates.map((template) => (
                                                <SelectItem key={template.id} value={template.id.toString()}>
                                                    {template.alias} - {template.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.report_template_id} />
                                </div>
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="month">Month</Label>
                                    <Select
                                        value={data.month}
                                        onValueChange={(value) => setData('month', value)}
                                    >
                                        <SelectTrigger id="month">
                                            <SelectValue placeholder="Select month" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="1">January</SelectItem>
                                            <SelectItem value="2">February</SelectItem>
                                            <SelectItem value="3">March</SelectItem>
                                            <SelectItem value="4">April</SelectItem>
                                            <SelectItem value="5">May</SelectItem>
                                            <SelectItem value="6">June</SelectItem>
                                            <SelectItem value="7">July</SelectItem>
                                            <SelectItem value="8">August</SelectItem>
                                            <SelectItem value="9">September</SelectItem>
                                            <SelectItem value="10">October</SelectItem>
                                            <SelectItem value="11">November</SelectItem>
                                            <SelectItem value="12">December</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.month} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="year">Year</Label>
                                    <Select
                                        value={data.year}
                                        onValueChange={(value) => setData('year', value)}
                                    >
                                        <SelectTrigger id="year">
                                            <SelectValue placeholder="Select year" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Array.from({ length: 5 }, (_, i) => currentYear - 2 + i).map(year => (
                                                <SelectItem key={year} value={year.toString()}>
                                                    {year}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.year} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Input
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                />
                                <InputError message={errors.description} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="file">Upload Data File (Optional)</Label>
                                <div className="flex items-center gap-2">
                                    <Input
                                        id="file"
                                        type="file"
                                        onChange={handleFileChange}
                                    />
                                    <Button type="button" variant="outline">
                                        <FileUp className="mr-2 h-4 w-4" />
                                        Browse
                                    </Button>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                    Upload a CSV or Excel file with your report data
                                </p>
                                <InputError message={errors.file as unknown as string} />
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" asChild>
                                    <Link href={route('reports.index')}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    <Save className="mr-2 h-4 w-4" />
                                    Create Report
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
