import { Head } from '@inertiajs/react';
import { Download, FileCheck, FileCog, FileWarning } from 'lucide-react';
import { Link } from '@inertiajs/react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Reports',
        href: '/reports',
    },
    {
        title: 'Report Details',
        href: '#',
    },
];

export default function ReportShow() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Report Details" />
            
            <div className="flex flex-col gap-6 p-4">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">SLIK Report - April 2025</h1>
                        <p className="text-muted-foreground">Generated on April 10, 2025</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline">
                            <Download className="mr-2 h-4 w-4" />
                            Download
                        </Button>
                        <Button>
                            <FileCheck className="mr-2 h-4 w-4" />
                            Submit Report
                        </Button>
                    </div>
                </div>
                
                <div className="grid gap-6 md:grid-cols-3">
                    <Card>
                        <CardHeader>
                            <CardTitle>Report Status</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-2">
                                <FileCog className="h-5 w-5 text-yellow-500" />
                                <span className="font-medium">Draft</span>
                            </div>
                            <p className="mt-2 text-sm text-muted-foreground">
                                This report is in draft status and has not been submitted yet.
                            </p>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader>
                            <CardTitle>Validation</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-2">
                                <FileWarning className="h-5 w-5 text-red-500" />
                                <span className="font-medium">3 Issues Found</span>
                            </div>
                            <p className="mt-2 text-sm text-muted-foreground">
                                There are validation issues that need to be resolved before submission.
                            </p>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader>
                            <CardTitle>Report Type</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="font-medium">Monthly SLIK Report</p>
                            <p className="mt-2 text-sm text-muted-foreground">
                                Template: D01 - Debitur Perseorangan
                            </p>
                        </CardContent>
                    </Card>
                </div>
                
                <Card>
                    <CardHeader>
                        <CardTitle>Report Data</CardTitle>
                        <CardDescription>
                            Summary of data included in this report
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-medium">Data Summary</h3>
                                <div className="mt-2 grid grid-cols-2 gap-4 md:grid-cols-4">
                                    <div className="rounded-lg border p-3">
                                        <p className="text-sm text-muted-foreground">Total Records</p>
                                        <p className="text-2xl font-bold">1,245</p>
                                    </div>
                                    <div className="rounded-lg border p-3">
                                        <p className="text-sm text-muted-foreground">Individual Debtors</p>
                                        <p className="text-2xl font-bold">987</p>
                                    </div>
                                    <div className="rounded-lg border p-3">
                                        <p className="text-sm text-muted-foreground">Corporate Debtors</p>
                                        <p className="text-2xl font-bold">258</p>
                                    </div>
                                    <div className="rounded-lg border p-3">
                                        <p className="text-sm text-muted-foreground">Total Loan Value</p>
                                        <p className="text-2xl font-bold">Rp 45.7B</p>
                                    </div>
                                </div>
                            </div>
                            
                            <Separator />
                            
                            <div>
                                <h3 className="text-lg font-medium">Validation Issues</h3>
                                <div className="mt-2 space-y-2">
                                    <div className="rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950">
                                        <p className="font-medium text-red-800 dark:text-red-200">Missing KTP Number for 2 debtors</p>
                                        <p className="text-sm text-red-700 dark:text-red-300">Records #123, #456</p>
                                    </div>
                                    <div className="rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950">
                                        <p className="font-medium text-red-800 dark:text-red-200">Invalid loan start date</p>
                                        <p className="text-sm text-red-700 dark:text-red-300">Record #789</p>
                                    </div>
                                    <div className="rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950">
                                        <p className="font-medium text-red-800 dark:text-red-200">Missing collateral information</p>
                                        <p className="text-sm text-red-700 dark:text-red-300">Record #234</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
