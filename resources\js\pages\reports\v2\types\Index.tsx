import { Head, <PERSON>, router } from '@inertiajs/react';
import { toast } from 'sonner';
import { <PERSON>, Pencil, Trash2 } from 'lucide-react';

import { ReportType } from '@/types/reports';
import AppLayout from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface IndexProps {
  reportTypes: ReportType[];
}

export default function ReportTypeIndex({ reportTypes }: IndexProps) {
  const handleDelete = (id: string) => {
    router.delete(route('report-types.destroy', id), {
      onSuccess: () => {
        toast.success('Tipe report berhasil dihapus.');
      },
      onError: (errors) => {
        toast.error(errors.delete || 'Gagal menghapus tipe report.');
      },
    });
  };

  // Kode untuk tabel dan aksi akan direfaktor untuk menggunakan Shadcn UI

  return (
    <AppLayout>
      <Head title="Tipe Hybrid Report" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div className="p-6 bg-white border-b border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold">Tipe Hybrid Report</h2>
                <Link href={route('report-types.create')}>
                  <Button variant="default">Tambah Tipe Report Baru</Button>
                </Link>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Nama</TableHead>
                      <TableHead>Deskripsi</TableHead>
                      <TableHead>Jumlah Field</TableHead>
                      <TableHead>Tanggal Dibuat</TableHead>
                      <TableHead>Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reportTypes.map((type) => (
                      <TableRow key={type.id}>
                        <TableCell>
                          <Badge variant="secondary">{type.id}</Badge>
                        </TableCell>
                        <TableCell>{type.name}</TableCell>
                        <TableCell>{type.description || '-'}</TableCell>
                        <TableCell>{type.field_definitions?.fields?.length || 0}</TableCell>
                        <TableCell>{new Date(type.created_at).toLocaleString()}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Link href={route('report-types.show', type.id)}>
                              <Button variant="default" size="sm" className="flex items-center gap-1">
                                <Eye className="h-4 w-4" />
                                Detail
                              </Button>
                            </Link>
                            <Link href={route('report-types.edit', type.id)}>
                              <Button variant="outline" size="sm" className="flex items-center gap-1">
                                <Pencil className="h-4 w-4" />
                                Edit
                              </Button>
                            </Link>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="destructive" size="sm" className="flex items-center gap-1">
                                  <Trash2 className="h-4 w-4" />
                                  Hapus
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Apakah Anda yakin ingin menghapus tipe report ini?
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Batal</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleDelete(type.id)}>
                                    Hapus
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
