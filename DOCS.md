# 📋 Persiapan Project
## Centralized SLIK Reporting — Panin Dubai Syariah Bank

---

## 1. Informasi Umum
| Item | Detail |
|:-----|:------|
| Nama Proyek | Centralized SLIK Reporting System |
| Divisi Terkait | IT Development, Risk Management, Compliance, Credit Admin |
| Stakeholder Utama | - |
| Target Go Live |-|
| Lokasi Server | On-Premise |

---

## 2. <PERSON>ftar La<PERSON>an yang Akan Dibuat
_(Daftar ini diambil dari hasil meeting pertama)_

| No | Nama Laporan | Alias | Format Output | Keterangan |
|:--|:-------------|:---------|:-------------|:----------|
| 1 |  Debitur Perseorangan | D01 | TXT |  |
| 2 |  Debitur Badan Usaha | D02 | TXT |  |
| 3 |  Fasilitas Kredit atau Pembiayaan| F01 | TXT |  |
| 4 |  Fasilitas Surat Berharga | F03 | TXT | |
| 5 |  Fasilitas Garansi yang Diberikan | F05| TXT | |
| 6 |  Agunan | P01| TXT | |

---

## 3. Dokumen Mapping Data
_(Dokumen mapping sudah diberikan di meeting pertama, checklist di bawah ini untuk memastikan kesiapan penggunaan)_

| No | Sumber Data | Field Internal | Field SLIK  | Catatan |
|:--|:------------|:--------------|:-----------|:---------------------------|
| 1 | DWH - ST_CUSTOMER | customer_id | id_debitur | Ya |

---

## 4. Proses Bisnis

### 4.1 Current Business Process (As-Is)
1. Pembentukan data Debitur segmen D01 dan D02 by inquery SQL kedalam DWH
2. Pembentukan data Fasilitas F01 – Pembiayaan baru dapat dijalankan setelah laporan LBUT selesai antara tgl 5-6 setiap bulannya dilakukan dengan metode vlookup
3. Pembentukan data Fasilitas F03 – Surat Berharga dilakukan manual by excel berdasarkan proofsheet SB
4. Pembentukan data fasilitas F05 – Bank Guarantee dilakukan manual by excel berdasarkan proofsheet BG
5. Pembentukan data A01 – Agunan dilakukan manual by excel berdasarkan data FIN-AGU
6. Updating OS Hapus Buku dilakukan manual by excel dengan metode vlookup
7. Updating data pembiayaan meliputi OS, Kolektibilitas, tunggakan, DPD dsb. dilakukan manual by excel dengan metode vlookup
8. Updating data Agunan yang telah dilakukan retaksasi dilakukan secara manual by excel dengan metode vlookup

#### 4.1.1 Flow PROSES PELAPORAN DATA BARU SLIK

    Source : F01_BulanLalu/Nominatif Bulan Lalu, ST_Customer, Krpdaily, ST_Collateral

- STEP 1 : Query & Import ke ms access

    Jalankan query sql ssms (Menggunakan Key Customer _No untuk di ST_Customer, iddebitur untuk di krpdaily) untuk mendapatkan data st_customer berdasarkan rekening pd krpdaily, hasil query import ke Ms Access. (file query ssms terlampir). import juga st_collateral di dwh ke access.
STEP 2  Lookup untuk provide Data Baru
Lakukan Lookup data pada KREDIT_PEMBIAYAAN_LBUT_DAILY (KRPDAILY)  Vs F01_BulanLalu/ Nominatif Bulan Lalu. Rekening yg Ada pada krpdaily namun tidak ada pada F01_BulanLalu merupakan data baru. Rekening yg tidak ada pada kprdaily namun ada pada F01_BulanLalu merupakan data Lunas. Hasil Lookup krpdaily data baru save ke Excel untuk dijadikan dasar Form D01,D02,A01.

STEP 3  Bentuk Data D01_Baru & D02_Baru
Dari Excel krpdaily_data_baru filter kolom “golonganPihakLawan”, Kode S14 merupakan source data untuk Form D01 (Nasabah Individu), Kode Selain S14 merupakan source data untuk Form D02 (Nasabah Badan). Import data baru (D01 & D02) sesuai format OJK ke ms access lakukan update data (lookup) melalui query access (Query design D01/D02_Baru & st_customer).
STEP 4  Bentuk  data A01_Baru
Bentuk data A01_Baru sesuai format OJK, ambil data norekening dari krpdaily_data_baru kemudian import file A01_Baru ke Ms Access, lakukan update data (lookup) melalui query design access ( Query Design A01_Baru & st_collateral).
STEP 5  Bentuk data F01_BulanLaporan
Setelah Nominativ/LBUT Final diterima, Import data tsb ke Access lakukan query update (Lookup) LBUT pada ms access sesuai format F01_BulanLaporan OJK.(Query design F01_BulanLaporan & 00_LBUT. Lakukan update manual untuk data restruktur,macet, frekuensi perpanjangan. Lakukan validasi pd ms access untuk crosscek.


### 4.2 Proposed Business Process (To-Be)
1. Semua proses yang dilakukan secara manual dapat dilakukan secara otomasi by system.

---

## 5. Functional Requirements

### 5.1 Functional Requirements

| Req# | Priority | Description | Use Case Reference |
|:-----|:---------|:------------|:-------------------|
| **General / Base Functionality** |  |  |  |
| FR-G-001 |  | • Menurunkan data dari DWH<br>• Upload /impor proofsheet manual<br>• Updating data<br>• Validasi Data tidak wajar & Disparitas LBUT<br>• Export txt file |  |
| FR-G-002 |  |  |  |
| FR-G-003 |  |  |  |
| **Reporting Requirements** |  |  |  |
| FR-R-001 |  |  |  |
| FR-R-002 |  |  |  |
| **Usability Requirements** |  |  |  |
| FR-U-001 |  |  |  |

## 6. Checklist Teknis Awal
| Checklist | Status | Catatan |
|:-----------|:-------|:--------|
| Database untuk Warehouse dibuat | Belum / Sudah | Nama DB: _______________ |
| Akses ke semua sumber data tersedia | Belum / Sudah | CBS, LOS, CRM |
| Tools ETL dipilih | Belum / Sudah | [Contoh: Apache NiFi, Script Manual, Airflow] |
| Design Data Model selesai | Belum / Sudah | Entity Relationship Diagram (ERD) |
| Server untuk Processing disiapkan | Belum / Sudah | CPU, RAM, Storage |
| Dokumentasi Mapping diverifikasi semua stakeholder | Belum / Sudah | Approval: Risk, Compliance, IT |

---

## 7. Risiko Awal yang Diidentifikasi
| Risiko | Dampak | Mitigasi |
|:-------|:-------|:---------|
| Kualitas data buruk | Data error saat submit ke OJK | Buat proses data cleansing otomatis |
| Perubahan format dari OJK | Keterlambatan project | Buat sistem modular agar mudah diupdate |
| Akses ke sistem sumber lambat | Menghambat ETL | Koordinasi dengan IT Infrastruktur |

---

## 8. Timeline Awal
_(Estimasi sebelum Rencana Proyek Detil dibuat)_

| Aktivitas | Start Date | End Date | PIC |
|:----------|:-----------|:---------|:----|
| Review Mapping Data | [Tanggal] | [Tanggal] | [Nama] |
| Setup Database dan Server | [Tanggal] | [Tanggal] | [Nama] |
| Pembangunan ETL Pipeline | [Tanggal] | [Tanggal] | [Nama] |
| Pembuatan Reporting API/File Generator | [Tanggal] | [Tanggal] | [Nama] |
| UAT & Validasi Laporan | [Tanggal] | [Tanggal] | [Nama] |
| Go Live | [Tanggal] | [Tanggal] | [Nama] |

---

## 9. Struktur Data Laporan

### D01 - Debitur Perseorangan

| No | Nama Field | Tipe Data | Panjang | Mandatory | Korelasi Sistem | Validasi | Keterangan |
|:---|:-----------|:----------|:--------|:----------|:----------------|:---------|:-----------|
| 1 | FLAG_DETAIL | CHAR | 1 | Y | - | Default 'D' | Flag untuk operasi pada sistem OJK |
| 2 | CUSTOMER_NO | VARCHAR | - | Y | - | Primary Key | Nomor CIF Debitur |
| 3 | LEGAL_DOC_NAME | VARCHAR | - | Y | - | - | Jenis Identitas |
| 4 | LEGAL_ID | VARCHAR | - | Y | - | - | Nomor Identitas |
| 5 | NAME_1 | VARCHAR | - | Y | - | - | Nama Sesuai Identitas & Nama Lengkap |
| 6 | GENDER | VARCHAR | - | Y | - | - | Jenis Kelamin |
| 7 | PLACE_OF_BIRTH | VARCHAR | - | Y | - | - | Tempat Lahir |
| 8 | DATE_OF_BIRTH | DATE | - | Y | - | - | Tanggal Lahir |
| 9 | CUS_NPWP | VARCHAR | - | Y | - | - | Nomor Pokok Wajib Pajak |
| 10 | MOTHER_MAID_NAM | VARCHAR | - | Y | - | - | Nama Gadis Ibu Kandung |
| 11 | ADDRESS | TEXT | - | Y | - | - | Alamat |
| 12 | KTP_KELURAHAN | VARCHAR | - | Y | - | - | Kelurahan |
| 13 | KTP_KECAMATAN | VARCHAR | - | Y | - | - | Kecamatan |
| 14 | SID_DATI2DEBTOR | VARCHAR | - | Y | - | - | Kode Kabupaten atau Kota |
| 15 | POST_CODE | VARCHAR | - | Y | - | - | Kode Pos |
| 16 | PHONE_1 | VARCHAR | - | Y | - | - | Nomor Telepon |
| 17 | SMS_1 | VARCHAR | - | Y | - | - | Nomor Telepon Seluler |
| 18 | EMAIL_1 | VARCHAR | - | Y | - | - | Alamat Surat Elektronik |
| 19 | NATIONALITY | VARCHAR | - | Y | - | - | Kode Negara Domisili |
| 20 | OCCUPATION | VARCHAR | - | Y | - | - | Kode Pekerjaan |
| 21 | EMPLOYERS_NAME | VARCHAR | - | Y | - | - | Tempat Bekerja |
| 22 | SID_JENIS_USAHA | VARCHAR | - | Y | - | - | Kode Bidang Usaha Tempat Bekerja |
| 23 | EMPLOYERS_ADD | TEXT | - | Y | - | - | Alamat Tempat Bekerja |
| 24 | LAST_EDUCATION | VARCHAR | - | Y | - | - | Kode Status Pendidikan atau Gelar Debitur |
| 25 | KYC_INCOM_RNG | VARCHAR | - | Y | - | - | Penghasilan Kotor PerTahun |
| 26 | KYC_INCOME_SRC | VARCHAR | - | Y | - | - | Kode Sumber Penghasilan |
| 27 | NO_OF_DEPENDENTS | INT | - | Y | - | - | Jumlah Tanggungan |
| 28 | SID_HUB_BANK | VARCHAR | - | Y | - | - | Kode Hubungan dengan Pelapor |
| 29 | LBU_GOL_DEB | VARCHAR | - | Y | - | - | Kode Golongan Debitur |
| 30 | MARITAL_STATUS | VARCHAR | - | Y | - | - | Status Perkawinan Debitur |
| 31 | SPOUSE_ID | VARCHAR | - | N | - | - | Nomor Identitas Pasangan |
| 32 | SPOUSE_NAME | VARCHAR | - | N | - | - | Nama Pasangan |
| 33 | SPOU_DT_OF_BIRT | DATE | - | N | - | - | Tanggal Lahir Pasangan |
| 34 | L_PISAH_HARTA | BOOLEAN | - | Y | - | - | Perjanjian Pisah Harta |
| 35 | SID_MELANGGAR | BOOLEAN | - | Y | - | - | Melanggar BMPK/BMPD/BMPP |
| 36 | SID_MELAMPAUI | BOOLEAN | - | Y | - | - | Melampaui BMPK/BMPD/BMPP |
| 37 | BRANCH_CODE | VARCHAR | - | N | - | - | Kode Kantor Cabang |
| 38 | DATA_OPERATION | CHAR | 1 | Y | - | In ('C','U','N') | Operasi Data |

Keterangan:
- **Tipe Data**: Format data yang digunakan (VARCHAR, TEXT, DATE, BOOLEAN, INT, CHAR)
- **Panjang**: Jumlah karakter maksimum yang diperbolehkan (akan dilengkapi sesuai spesifikasi SLIK)
- **Mandatory**: Y (Ya) / N (Tidak)
- **Korelasi Sistem**: Referensi ke tabel/field di sistem sumber (akan dilengkapi)
- **Validasi**: Aturan validasi yang harus dipenuhi
- **Keterangan**: Informasi tambahan atau contoh isian

### Contoh Output Laporan D01 (Pipe Separated)

#### Header
FLAG_DETAIL|CUSTOMER_NO|LEGAL_DOC_NAME|LEGAL_ID|NAME_1|NAME_2|GENDER_CODE|GENDER|PLACE_OF_BIRTH|DATE_OF_BIRTH|CUS_NPWP|ADDRESS|KTP_KELURAHAN|KTP_KECAMATAN|SID_DATI2DEBTOR|POST_CODE|PHONE_1|SMS_1|EMAIL_1|NATIONALITY|OCCUPATION|EMPLOYERS_NAME|SID_JENIS_USAHA|EMPLOYERS_ADD|LAST_EDUCATION|KYC_INCOM_RNG|KYC_INCOME_SRC|NO_OF_DEPENDENTS|SID_HUB_BANK|LBU_GOL_DEB|MARITAL_STATUS|SPOUSE_ID|SPOUSE_NAME|SPOU_DT_OF_BIRT|L_PISAH_HARTA|SID_MELANGGAR|SID_MELAMPAUI|MOTHER_MAID_NAM|BRANCH_CODE|DATA_OPERATION

#### Value
D|**********|1|214000000000000000000|MARTINI|MARTINI|00|P|PAUH MANIS|********||LIMA HINDU|BATU KALANG|PADANG SAGO|3405|25573|0|************||ID|099|NA|960009||0||0|N|S14|2||||T|T|T|MARAH|001|U

### Penjelasan Format:
- Setiap field dipisahkan dengan karakter pipe (|)
- Field kosong tetap mempertahankan separator (||)
- Tidak ada spasi di antara separator
- Total field: 38 kolom
- Format tanggal menggunakan YYYYMMDD (contoh: ********)

### Validasi Data:
1. Jumlah kolom pada header: 38 kolom
2. Jumlah kolom pada value: 38 kolom
3. Kesesuaian urutan: Header dan value memiliki urutan yang sama
4. Flag awal: Selalu dimulai dengan 'D' untuk detail record
5. Flag akhir: Diakhiri dengan operation code ('U' untuk Update)

### Catatan Penting:
- Pastikan jumlah field pada header dan value selalu sama (38 kolom)
- Setiap baris data harus diakhiri dengan newline (CRLF)
- Tidak boleh ada spasi di awal atau akhir setiap field
- Field mandatory yang kosong tetap harus divalidasi sebelum pengiriman

---

## 🔥 Catatan Tambahan
- Semua mapping field harus disetujui Compliance dan Risk sebelum pembangunan dimulai.
- Semua laporan harus melewati proses UAT (User Acceptance Testing) dengan contoh data riil.
- Perlu buat **error handling** jika ada field kosong atau salah format sebelum submit ke OJK.
