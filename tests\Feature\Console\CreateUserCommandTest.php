<?php

namespace Tests\Feature\Console;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CreateUserCommandTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_creates_a_new_user(): void
    {
        $this->artisan('user:create', [
            '--name' => 'Test User',
            '--email' => '<EMAIL>',
            '--password' => 'password123',
        ])
            ->expectsOutputToContain('User created successfully!')
            ->assertExitCode(0);

        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNull($user->email_verified_at);
    }

    public function test_it_creates_a_verified_user(): void
    {
        $this->artisan('user:create', [
            '--name' => 'Verified User',
            '--email' => '<EMAIL>',
            '--password' => 'password123',
            '--verify' => true,
        ])
            ->expectsOutputToContain('User email has been verified.')
            ->expectsOutputToContain('User created successfully!')
            ->assertExitCode(0);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNotNull($user->email_verified_at);
    }

    public function test_it_validates_email_format(): void
    {
        $this->artisan('user:create', [
            '--name' => 'Invalid User',
            '--email' => 'not-an-email',
            '--password' => 'password123',
        ])
            ->expectsOutputToContain('The email field must be a valid email address')
            ->assertExitCode(1);

        $this->assertDatabaseMissing('users', [
            'name' => 'Invalid User',
        ]);
    }

    public function test_it_validates_unique_email(): void
    {
        // Create a user first
        User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Try to create another user with the same email
        $this->artisan('user:create', [
            '--name' => 'Duplicate User',
            '--email' => '<EMAIL>',
            '--password' => 'password123',
        ])
            ->expectsOutputToContain('The email has already been taken')
            ->assertExitCode(1);
    }
}
