<?php

namespace Database\Seeders;

use App\Models\ReportElement;
use Illuminate\Database\Seeder;

class ReportElementSeeder extends Seeder
{
    public function run(): void
    {
        $elements = [
            // Elements untuk D01 - Debitur Perseorangan
            [
                'label' => 'Flag Detail',
                'field' => 'flag_detail',
                'type' => 'char',
            ],
            [
                'label' => 'Nomor CIF',
                'field' => 'customer_no',
                'type' => 'string',
            ],
            [
                'label' => 'Jenis Identitas',
                'field' => 'legal_doc_name',
                'type' => 'string',
            ],
            [
                'label' => 'Nomor Identitas',
                'field' => 'legal_id',
                'type' => 'string',
            ],
            [
                'label' => 'Nama Sesuai I<PERSON>itas',
                'field' => 'name_1',
                'type' => 'string',
            ],
            [
                'label' => 'Nama Lengkap',
                'field' => 'name_2',
                'type' => 'string',
            ],
            [
                'label' => 'Kode Jenis Kelamin',
                'field' => 'gender_code',
                'type' => 'string',
            ],
            [
                'label' => 'Jenis Kelamin',
                'field' => 'gender',
                'type' => 'string',
            ],
            [
                'label' => 'Tempat Lahir',
                'field' => 'place_of_birth',
                'type' => 'string',
            ],
            [
                'label' => 'Tanggal Lahir',
                'field' => 'date_of_birth',
                'type' => 'date',
            ],
            [
                'label' => 'NPWP',
                'field' => 'cus_npwp',
                'type' => 'string',
            ],
            [
                'label' => 'Alamat',
                'field' => 'address',
                'type' => 'string',
            ],
            [
                'label' => 'Kelurahan',
                'field' => 'ktp_kelurahan',
                'type' => 'string',
            ],
            [
                'label' => 'Kecamatan',
                'field' => 'ktp_kecamatan',
                'type' => 'string',
            ],
            [
                'label' => 'Dati II',
                'field' => 'sid_dati2debtor',
                'type' => 'string',
            ],
            [
                'label' => 'Kode Pos',
                'field' => 'post_code',
                'type' => 'string',
            ],
            [
                'label' => 'Nomor Telepon',
                'field' => 'phone_1',
                'type' => 'string',
            ],
            [
                'label' => 'Nomor HP',
                'field' => 'sms_1',
                'type' => 'string',
            ],
            [
                'label' => 'Email',
                'field' => 'email_1',
                'type' => 'string',
            ],
            [
                'label' => 'Kewarganegaraan',
                'field' => 'nationality',
                'type' => 'string',
            ],
            [
                'label' => 'Pekerjaan',
                'field' => 'occupation',
                'type' => 'string',
            ],
            [
                'label' => 'Nama Perusahaan',
                'field' => 'employers_name',
                'type' => 'string',
            ],
            [
                'label' => 'Jenis Usaha',
                'field' => 'sid_jenis_usaha',
                'type' => 'string',
            ],
            [
                'label' => 'Alamat Perusahaan',
                'field' => 'employers_add',
                'type' => 'string',
            ],
            [
                'label' => 'Pendidikan Terakhir',
                'field' => 'last_education',
                'type' => 'string',
            ],
            [
                'label' => 'Range Penghasilan',
                'field' => 'kyc_incom_rng',
                'type' => 'string',
            ],
            [
                'label' => 'Sumber Penghasilan',
                'field' => 'kyc_income_src',
                'type' => 'string',
            ],
            [
                'label' => 'Jumlah Tanggungan',
                'field' => 'no_of_dependents',
                'type' => 'string',
            ],
            [
                'label' => 'Hubungan dengan Bank',
                'field' => 'sid_hub_bank',
                'type' => 'string',
            ],
            [
                'label' => 'Golongan Debitur',
                'field' => 'lbu_gol_deb',
                'type' => 'string',
            ],
            [
                'label' => 'Status Perkawinan',
                'field' => 'marital_status',
                'type' => 'string',
            ],
            [
                'label' => 'ID Pasangan',
                'field' => 'spouse_id',
                'type' => 'string',
            ],
            [
                'label' => 'Nama Pasangan',
                'field' => 'spouse_name',
                'type' => 'string',
            ],
            [
                'label' => 'Tanggal Lahir Pasangan',
                'field' => 'spou_dt_of_birt',
                'type' => 'date',
            ],
            [
                'label' => 'Status Pisah Harta',
                'field' => 'l_pisah_harta',
                'type' => 'char',
            ],
            [
                'label' => 'Status Melanggar BMPK',
                'field' => 'sid_melanggar',
                'type' => 'char',
            ],
            [
                'label' => 'Status Melampaui BMPK',
                'field' => 'sid_melampaui',
                'type' => 'char',
            ],
            [
                'label' => 'Nama Gadis Ibu Kandung',
                'field' => 'mother_maid_nam',
                'type' => 'string',
            ],
            [
                'label' => 'Kode Cabang',
                'field' => 'branch_code',
                'type' => 'string',
            ],
            [
                'label' => 'Operasi Data',
                'field' => 'data_operation',
                'type' => 'char',
            ],
            [
                'label' => 'Kode Bentuk Badan Usaha',
                'field' => 'badan_hukum',
                'type' => 'string',
            ],
            [
                'label' => 'Tempat Pendirian',
                'field' => 'hm_town_country',
                'type' => 'string',
            ],
            [
                'label' => 'Nomor Akta Pendirian',
                'field' => 'no_akta_awal',
                'type' => 'string',
            ],
            [
                'label' => 'Tanggal Akta Pendirian',
                'field' => 'tgl_akta_awal',
                'type' => 'date',
            ],
            [
                'label' => 'Nomor Akta Perubahan Terakhir',
                'field' => 'no_akta_akhir',
                'type' => 'string',
            ],
            [
                'label' => 'Tanggal Akta Perubahan Terakhir',
                'field' => 'tgl_akta_akhir',
                'type' => 'date',
            ],
            [
                'label' => 'Go Public',
                'field' => 'sid_go_public',
                'type' => 'char',
            ],
            [
                'label' => 'Peringkat atau Rating Debitur',
                'field' => 'customer_rating',
                'type' => 'string',
            ],
            [
                'label' => 'Lembaga Pemeringkat',
                'field' => 'lembaga_pemeringkat',
                'type' => 'string',
            ],
            [
                'label' => 'Tanggal Pemeringkatan',
                'field' => 'rating_date',
                'type' => 'date',
            ],
            [
                'label' => 'Nama Grup Usaha Debitur',
                'field' => 'group_name',
                'type' => 'string',
            ],
        ];

        foreach ($elements as $element) {
            ReportElement::create($element);
        }
    }
}
