<?php

namespace Tests\Feature;

use App\Models\ImportNotification;
use App\Models\ReportBatch;
use App\Models\Reports\A01;
use App\Services\CsvImportService;
use App\Services\ReportImportService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test CSV file
        Storage::fake('local');
        $this->createTestCsvFile();
    }

    public function test_csv_filename_parsing()
    {
        $csvService = new CsvImportService();
        
        $result = $csvService->parseFilename('A01_042025.csv');
        
        $this->assertEquals('A01', $result['report_code']);
        $this->assertEquals(4, $result['month']);
        $this->assertEquals(2025, $result['year']);
    }

    public function test_csv_separator_detection()
    {
        $csvService = new CsvImportService();

        $separator = $csvService->detectSeparator('seed/A01_042025.csv');

        $this->assertEquals(';', $separator);
    }

    public function test_csv_parsing()
    {
        $csvService = new CsvImportService();

        $result = $csvService->parseCsv('seed/A01_042025.csv');

        $this->assertArrayHasKey('headers', $result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('separator', $result);
        $this->assertArrayHasKey('total_rows', $result);

        $this->assertCount(2, $result['data']); // Should have 2 data rows
        $this->assertEquals(';', $result['separator']);
    }

    public function test_report_import_creates_batch_and_data()
    {
        $importService = new ReportImportService(new CsvImportService());

        // Ensure no existing data
        $this->assertEquals(0, ReportBatch::count());
        $this->assertEquals(0, A01::count());
        $this->assertEquals(0, ImportNotification::count());

        // Perform import
        $notification = $importService->importFromCsv('seed/A01_042025.csv');

        // Debug: Check notification status
        if ($notification->status === 'failed') {
            $this->fail('Import failed: ' . $notification->error_message);
        }

        // Check that batch was created
        $this->assertEquals(1, ReportBatch::count());
        $batch = ReportBatch::first();
        $this->assertEquals('A01', $batch->report_code);
        $this->assertEquals(4, $batch->report_month);
        $this->assertEquals(2025, $batch->report_year);
        $this->assertEquals('completed', $batch->status);

        // Check that data was imported
        $this->assertEquals(2, A01::count());

        // Check notification was created
        $this->assertEquals(1, ImportNotification::count());
        $this->assertEquals('success', $notification->status);
        $this->assertEquals(2, $notification->records_imported);
    }

    public function test_import_replaces_existing_data()
    {
        $importService = new ReportImportService(new CsvImportService());

        // First import
        $notification1 = $importService->importFromCsv('seed/A01_042025.csv');
        $this->assertEquals(2, A01::count());

        // Second import should replace data
        $notification2 = $importService->importFromCsv('seed/A01_042025.csv');
        $this->assertEquals(2, A01::count()); // Still 2 records, not 4

        // Should have 2 notifications
        $this->assertEquals(2, ImportNotification::count());

        // But still only 1 batch
        $this->assertEquals(1, ReportBatch::count());
    }

    private function createTestCsvFile()
    {
        $csvContent = "FLAG DETAIL;KODE REGISTER / NOMOR AGUNAN;NOMOR REKENING FASILITAS;NOMOR CIF DEBITUR;KODE JENIS SEGMEN FASILITAS;KODE STATUS AGUNAN;KODE JENIS AGUNAN;PERINGKAT AGUNAN;KODE LEMBAGA PEMERINGKAT;KODE JENIS PENGIKATAN;TANGGAL PENGIKATAN;NAMA PEMILIK AGUNAN;BUKTI KEPEMILIKAN;ALAMAT AGUNAN;KODE KAB/KOTA (DATI 2) LOKASI AGUNAN;NILAI AGUNAN SESUAI NJOP;NILAI AGUNAN MENURUT LJK;TANGGAL PENILAIAN LJK;NILAI AGUNAN PENILAI INDEPENDEN;NAMA PENILAI INDEPENDEN;TANGGAL PENILAIAN PENILAI INDEPENDEN;STATUS PARIPASU;PERSENTASE PARIPASU;STATUS KREDIT JOIN;DIASURANSIKAN;KETERANGAN;KODE KANTOR CABANG;OPERASI DATA\n";
        $csvContent .= "D;86086000008880001;F0000508533;8600000888;F01;1;AN0299;;;99;20230309;HASANI;SPPH A,N HASANI;JL, MUTIARA MARDIKA RIJALI SIRIMAU;8191;25000000;25000000;20230309;;;;T;;T;T;;008;U\n";
        $csvContent .= "D;86086000003810001;F0000494207;8600000381;F01;1;AN0299;;;99;20220915;ARAS ARTAN;SPPH A,N ARAS ARTAN;DEBOWAE, RT 000 RW 000, KEL KEC KAB PROVINSI MALUKU DEBOWAY, WAIAPO,;8104;50000000;50000000;20220915;;;;T;;T;Y;;008;U\n";

        Storage::put('seed/A01_042025.csv', $csvContent);
    }
}
