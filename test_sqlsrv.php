<?php

/**
 * Microsoft SQL Server Connection Test Script
 *
 * This script tests the connection to a Microsoft SQL Server database
 * using both the sqlsrv and PDO_SQLSRV extensions.
 *
 * Before running this script, make sure you have:
 * 1. Installed the Microsoft ODBC Driver for SQL Server
 * 2. Installed the sqlsrv and pdo_sqlsrv PHP extensions
 * 3. Enabled the extensions in your php.ini file
 *
 * Usage: php test_sqlsrv.php
 */

// Configuration - Replace with your actual SQL Server details
$serverName = 'your_server_name'; // e.g., "localhost" or "*************" or "server.domain.com"
$databaseName = 'your_database_name';
$username = 'your_username';
$password = 'your_password';

// Display PHP version and loaded extensions
echo 'PHP Version: '.PHP_VERSION."\n";
echo "Loaded Extensions:\n";
$extensions = get_loaded_extensions();
sort($extensions);
foreach ($extensions as $extension) {
    echo "- $extension\n";
}
echo "\n";

// Function to test if an extension is loaded
function check_extension($extension_name)
{
    if (extension_loaded($extension_name)) {
        echo "✅ $extension_name extension is loaded.\n";

        return true;
    } else {
        echo "❌ $extension_name extension is NOT loaded.\n";
        echo "   Please make sure the extension is installed and enabled in your php.ini file.\n";

        return false;
    }
}

// Check for required extensions
echo "Checking required extensions:\n";
$sqlsrv_loaded = check_extension('sqlsrv');
$pdo_sqlsrv_loaded = check_extension('pdo_sqlsrv');
echo "\n";

// Test connection using sqlsrv extension
if ($sqlsrv_loaded) {
    echo "Testing connection using sqlsrv extension:\n";
    try {
        // Connection options
        $connectionOptions = [
            'Database' => $databaseName,
            'Uid' => $username,
            'PWD' => $password,
            // Optional settings
            'TrustServerCertificate' => true, // For self-signed certificates
            'Encrypt' => true, // For secure connections
            'ConnectRetryCount' => 3, // Retry connection 3 times
        ];

        // Establish the connection
        $conn = sqlsrv_connect($serverName, $connectionOptions);

        if ($conn === false) {
            echo "❌ Connection failed using sqlsrv extension.\n";
            echo '   Error: '.print_r(sqlsrv_errors(), true)."\n";
        } else {
            echo "✅ Connection successful using sqlsrv extension!\n";

            // Test a simple query
            $sql = 'SELECT @@VERSION as SQL_Server_Version';
            $stmt = sqlsrv_query($conn, $sql);

            if ($stmt === false) {
                echo '❌ Query failed: '.print_r(sqlsrv_errors(), true)."\n";
            } else {
                if (sqlsrv_fetch($stmt)) {
                    $version = sqlsrv_get_field($stmt, 0);
                    echo "   SQL Server Version: $version\n";
                }
                sqlsrv_free_stmt($stmt);
            }

            // Close the connection
            sqlsrv_close($conn);
        }
    } catch (Exception $e) {
        echo '❌ Exception: '.$e->getMessage()."\n";
    }
    echo "\n";
}

// Test connection using PDO_SQLSRV extension
if ($pdo_sqlsrv_loaded) {
    echo "Testing connection using PDO_SQLSRV extension:\n";
    try {
        // Create connection string
        $dsn = "sqlsrv:Server=$serverName;Database=$databaseName";

        // Connection options
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::SQLSRV_ATTR_ENCODING => PDO::SQLSRV_ENCODING_UTF8,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ];

        // Create a PDO instance
        $pdo = new PDO($dsn, $username, $password, $options);

        echo "✅ Connection successful using PDO_SQLSRV extension!\n";

        // Test a simple query
        $stmt = $pdo->query('SELECT @@VERSION as SQL_Server_Version');
        $row = $stmt->fetch();
        echo '   SQL Server Version: '.$row['SQL_Server_Version']."\n";

        // Close the connection
        $pdo = null;
    } catch (PDOException $e) {
        echo "❌ Connection failed using PDO_SQLSRV extension.\n";
        echo '   Error: '.$e->getMessage()."\n";
    }
    echo "\n";
}

// If neither extension is loaded, provide installation instructions
if (! $sqlsrv_loaded && ! $pdo_sqlsrv_loaded) {
    echo "Installation Instructions:\n";
    echo "1. Install the Microsoft ODBC Driver for SQL Server:\n";
    echo "   sudo apt-get install -y unixodbc-dev\n";
    echo "   curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -\n";
    echo "   curl https://packages.microsoft.com/config/ubuntu/22.04/prod.list | sudo tee /etc/apt/sources.list.d/mssql-release.list\n";
    echo "   sudo apt-get update\n";
    echo "   sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18\n\n";

    echo "2. Install the PHP extensions for SQL Server:\n";
    echo "   sudo apt-get install -y php-pear php-dev\n";
    echo "   sudo pecl install sqlsrv pdo_sqlsrv\n\n";

    echo "3. Enable the extensions in PHP:\n";
    echo "   sudo bash -c 'echo \"extension=sqlsrv.so\" > /etc/php/8.2/mods-available/sqlsrv.ini'\n";
    echo "   sudo bash -c 'echo \"extension=pdo_sqlsrv.so\" > /etc/php/8.2/mods-available/pdo_sqlsrv.ini'\n";
    echo "   sudo phpenmod -v 8.2 sqlsrv pdo_sqlsrv\n\n";

    echo "4. Restart your web server (if applicable):\n";
    echo "   sudo systemctl restart apache2\n";
    echo "   # or\n";
    echo "   sudo systemctl restart nginx\n";
}

echo "Test completed.\n";
