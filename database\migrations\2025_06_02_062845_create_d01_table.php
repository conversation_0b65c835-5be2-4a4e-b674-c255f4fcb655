<?php

use App\Models\ReportBatch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_d01', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(ReportBatch::class, 'report_batch_id')->constrained()->cascadeOnDelete();
            $table->string('flag_detail')->nullable();
            $table->string('nomor_cif_debitur')->nullable();
            $table->string('jenis_identitas')->nullable();
            $table->string('nik_atau_passport')->nullable();
            $table->string('nama_sesuai_identitas')->nullable();
            $table->string('nama_lengkap')->nullable();
            $table->string('kode_status_pendidikan_gelar')->nullable();
            $table->string('jenis_kelamin')->nullable();
            $table->string('tempat_lahir')->nullable();
            $table->date('tanggal_lahir')->nullable();
            $table->string('npwp')->nullable();
            $table->text('alamat')->nullable();
            $table->string('kelurahan')->nullable();
            $table->string('kecamatan')->nullable();
            $table->string('kode_kab_kota')->nullable();
            $table->string('kode_pos')->nullable();
            $table->string('telepon')->nullable();
            $table->string('nomor_telepon_seluler')->nullable();
            $table->string('alamat_email')->nullable();
            $table->string('kode_negara_domisili')->nullable();
            $table->string('kode_pekerjaan')->nullable();
            $table->string('tempat_bekerja')->nullable();
            $table->string('kode_bidang_usaha_tempat_bekerja')->nullable();
            $table->text('alamat_tempat_bekerja')->nullable();
            $table->decimal('penghasilan_kotor_per_tahun', 18, 2)->nullable();
            $table->string('kode_sumber_penghasilan')->nullable();
            $table->integer('jumlah_tanggungan')->nullable();
            $table->string('kode_hubungan_dengan_pelapor')->nullable();
            $table->string('kode_golongan_debitur')->nullable();
            $table->string('status_perkawinan')->nullable();
            $table->string('nomor_identitas_pasangan')->nullable();
            $table->string('nama_pasangan')->nullable();
            $table->date('tanggal_lahir_pasangan')->nullable();
            $table->boolean('perjanjian_pisah_harta')->nullable();
            $table->boolean('melanggar_bmpk')->nullable();
            $table->boolean('melampaui_bmpk')->nullable();
            $table->string('nama_gadis_ibu_kandung')->nullable();
            $table->string('kode_kantor_cabang')->nullable();
            $table->string('operasi_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reprot_d01');
    }
};
