import { Table } from "@tanstack/react-table";
import { X, Search, ChevronDown, Check } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableViewOptions } from "./data-table-view-options";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { elementTypes } from "@/pages/elements/columns";
import { useState } from "react";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  filterColumn: string;
  filterPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  onSearchSubmit?: () => void;
  typeFilter?: string;
  onTypeFilterChange?: (value: string) => void;
}

export function DataTableToolbar<TData>({
  table,
  filterColumn,
  filterPlaceholder = "Filter...",
  searchValue,
  onSearchChange,
  onSearchSubmit,
  typeFilter,
  onTypeFilterChange,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;
  const [isTypeOpen, setIsTypeOpen] = useState(false);

  // Handle client-side filtering
  const handleClientSideFilter = (value: string) => {
    table.getColumn(filterColumn)?.setFilterValue(value);
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (onSearchChange) {
      onSearchChange(value);
    } else {
      handleClientSideFilter(value);
    }
  };

  // Handle search form submit
  const handleSearchSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (onSearchSubmit) {
      onSearchSubmit();
    }
  };

  // Handle clear search
  const handleClearSearch = () => {
    if (onSearchChange) {
      onSearchChange("");
    } else {
      table.resetColumnFilters();
    }
  };

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
      <div className="flex flex-1 items-center space-x-2">
        <form onSubmit={handleSearchSubmit} className="flex w-full items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={filterPlaceholder}
              value={searchValue !== undefined ? searchValue : (table.getColumn(filterColumn)?.getFilterValue() as string) ?? ""}
              onChange={handleSearchChange}
              className="w-full pl-8"
            />
            {(searchValue || isFiltered) && (
              <Button
                type="button"
                variant="ghost"
                onClick={handleClearSearch}
                className="absolute right-0 top-0 h-full px-3"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear filter</span>
              </Button>
            )}
          </div>
          <Button type="submit">Search</Button>
        </form>
      </div>

      <div className="flex items-center gap-2">
        {onTypeFilterChange && (
          <Popover open={isTypeOpen} onOpenChange={setIsTypeOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[180px] justify-between">
                {typeFilter ? elementTypes.find(t => t.value === typeFilter)?.label : "Filter by Type"}
                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[180px] p-0">
              <Command>
                <CommandInput placeholder="Search type..." />
                <CommandList>
                  <CommandEmpty>No type found.</CommandEmpty>
                  <CommandGroup>
                    <CommandItem
                      onSelect={() => {
                        onTypeFilterChange("");
                        setIsTypeOpen(false);
                      }}
                      className="justify-between"
                    >
                      All Types
                      {!typeFilter && <Check className="h-4 w-4" />}
                    </CommandItem>
                    {elementTypes.map((type) => (
                      <CommandItem
                        key={type.value}
                        onSelect={() => {
                          onTypeFilterChange(type.value);
                          setIsTypeOpen(false);
                        }}
                        className="justify-between"
                      >
                        {type.label}
                        {typeFilter === type.value && <Check className="h-4 w-4" />}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        )}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
