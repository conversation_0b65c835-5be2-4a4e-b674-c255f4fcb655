<?php

namespace App\Console\Commands;

use App\Imports\ImportReportF01;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;

class CompareF01WithLbutDaily extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'compare:f01-lbut 
                           {--periode= : Periode laporan (format: YYYY-MM), default bulan lalu}
                           {--dry-run : Jalankan tanpa menyimpan data}
                           {--run-verbose : Tampilkan detail proses}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Komparasi data F01 dengan KP_LBUT_DAILY dan sinkronisasi data baru yang di temukan ke tabel kp_lbut_daily';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startTime = microtime(true);

        // Tentukan periode laporan
        $periode = $this->option('periode') ?? Carbon::now()->subMonth()->format('Y-m');
        $isDryRun = $this->option('dry-run');
        $isVerbose = $this->option('run-verbose');

        $this->info("=== Memulai Komparasi Data F01 vs KP_LBUT_DAILY ===");
        $this->info("Periode: {$periode}");
        $this->info("Mode: " . ($isDryRun ? "DRY RUN (tidak menyimpan data)" : "PRODUCTION"));
        $this->line("");

        try {
            // Step 1: Ambil data F01 periode bulan lalu
            $this->info("📊 Mengambil data F01 periode {$periode}...");
            $f01Data = $this->getF01Data($periode);
            $this->info("✅ Ditemukan " . count($f01Data) . " record F01");

            // Step 2: Ambil data KP_LBUT_DAILY
            $this->info("📊 Mengambil data KP_LBUT_DAILY...");
            $lbutData = $this->getLbutData();
            $this->info("✅ Ditemukan " . count($lbutData) . " record KP_LBUT_DAILY");

            if (count($f01Data) === 0 && count($lbutData) === 0) {
                $this->warn("⚠️  Tidak ada data untuk diproses");
                return 0;
            }

            // Step 3: Bandingkan dan temukan data yang tidak ada
            $this->info("🔍 Melakukan komparasi data...");

            // Data F01 yang tidak ada di LBUT
            $f01RekeningList = collect($f01Data)
                ->pluck('nomor_rekening_fasilitas')
                ->toArray();
            $lbutRekeningList = collect($lbutData)
                ->pluck('nomorRekening')
                ->toArray();

            $missingF01Data = $this->findForNewData($f01Data, $lbutRekeningList);
            $this->info("✅ Ditemukan " . count($missingF01Data) . " record F01 yang tidak ada di KP_LBUT_DAILY");

            // Step 4: Tampilkan detail jika verbose
            if ($isVerbose) {
                if (count($missingF01Data) > 0) {
                    $this->line("");
                    $this->info("=== DATA F01 YANG HILANG DI KP_LBUT_DAILY ===");
                    $this->displayMissingF01Details($missingF01Data);
                }
            }

            // Step 5: Insert data ke kp_lbut_daily
            if (!$isDryRun) {
                $totalInserted = 0;

                if (count($missingF01Data) > 0) {
                    $this->info("💾 Memasukkan data F01 yang hilang ke tabel kp_lbut_daily...");
                    $insertedF01 = $this->insertMissingF01Data($missingF01Data, $periode);
                    $this->info("✅ Berhasil memasukkan {$insertedF01} record dari F01");
                    $totalInserted += $insertedF01;
                }

                $this->info("🎉 Total {$totalInserted} record berhasil ditambahkan");
            } else {
                $this->warn("  - {count($missingF01Data)} dari F01");
            }

            // Summary
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            $this->line("");
            $this->info("=== RINGKASAN PROSES ===");
            $this->info("📈 Total F01 Records: " . count($f01Data));
            $this->info("📈 Total LBUT Records: " . count($lbutData));
            $this->info("🔍 Missing F01 Records: " . count($missingF01Data));
            $this->info("⏱️  Waktu Eksekusi: {$executionTime} detik");

            if (!$isDryRun) {
                $this->info("✅ Proses sinkronisasi berhasil diselesaikan");
            }

        } catch (\Exception $e) {
            dd("Error: \n", $e);
            $this->error("❌ Error: " . $e->getMessage());
            Log::error("Compare F01 LBUT Command Error", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'periode' => $periode
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Ambil data F01 berdasarkan periode
     */
    private function getF01Data($periode)
    {
        // Contoh: Membaca data dari file Excel dan mengambil kolom "NOMOR REKENING FASILITAS"
        $excelFilePath = storage_path('app' . DIRECTORY_SEPARATOR . 'private' . DIRECTORY_SEPARATOR . "seed" . DIRECTORY_SEPARATOR . "F01_042025.csv"); // Ganti path sesuai lokasi file Anda

        if (!file_exists($excelFilePath)) {
            $this->error("❌ File Excel F01 tidak ditemukan di: {$excelFilePath}");
            return [];
        }

        $rows = [];
        if (($handle = fopen($excelFilePath, 'r')) !== false) {
            while (($data = fgetcsv($handle, 0, ';')) !== false) {
                $rows[] = $data;
            }
            fclose($handle);
        }

        if (count($rows) < 2) {
            $this->error("❌ File CSV F01 tidak memiliki data yang cukup.");
            return [];
        }

        // Ambil header dan mapping kolom
        $header = $rows[0];
        $headerMap = [];
        foreach ($header as $col => $value) {
            $headerMap[$value] = $col;
        }

        $f01Rows = [];
        for ($i = 1; $i < count($rows); $i++) {
            $row = $rows[$i];
            $rekening = $row[$headerMap['NOMOR REKENING FASILITAS']] ?? null;
            if ($rekening) {
                $f01Rows[] = (object) [
                    'nomor_rekening_fasilitas' => $rekening,
                    'nomor_cif_debitur' => $row[$headerMap['NOMOR CIF DEBITUR']] ?? null,
                    // 'kode_jenis_kredit_pembiayaan' => $row[$headerMap['KODE JENIS KREDIT / PEMBIAYAAN']] ?? null,
                    // 'plafon' => isset($headerMap['PLAFON']) ? floatval(str_replace(',', '', $row[$headerMap['PLAFON']])) : null,
                    // 'baki_debet' => isset($headerMap['BAKI DEBET']) ? floatval(str_replace(',', '', $row[$headerMap['BAKI DEBET']])) : null,
                    // 'kode_kolektibilitas' => $row[$headerMap['KODE KOLEKTIBILITAS']] ?? null,
                    // 'kode_kantor_cabang' => $row[$headerMap['KODE KANTOR CABANG']] ?? null,
                    // 'tanggal_jatuh_tempo' => $row[$headerMap['TANGGAL JATUH TEMPO']] ?? null,
                ];
            }
        }

        return $f01Rows;

        // Temukan posisi kolom "NOMOR REKENING FASILITAS"
        // foreach ($rows as $rowIndex => $row) {
        //     if ($rowIndex === 1) {
        //         foreach ($row as $col => $value) {
        //             $headerMap[$value] = $col;
        //         }
        //         continue;
        //     }
        //     // Ambil data hanya jika kolom "NOMOR REKENING FASILITAS" ada dan tidak kosong
        //     $rekening = $row[$headerMap['NOMOR REKENING FASILITAS']] ?? null;
        //     if ($rekening) {
        //         $f01Rows[] = [
        //             'nomor_rekening_fasilitas' => $rekening,
        //             // Tambahkan kolom lain sesuai kebutuhan
        //         ];
        //     }
        // }

        // Jika hanya ingin array nomor rekening saja:
        // $rekeningList = array_column($f01Rows, 'nomor_rekening_fasilitas');
        // return DB::table('report_f01')
        //     ->select([
        //         'nomor_rekening_fasilitas',
        //         'nomor_cif_debitur',
        //         'kode_jenis_kredit_pembiayaan',
        //         'plafon',
        //         'baki_debet',
        //         'kode_kolektibilitas',
        //         'kode_kantor_cabang',
        //         'tanggal_jatuh_tempo'
        //     ])
        //     ->where('periode_laporan', $periode)
        //     ->whereNotNull('nomor_rekening_fasilitas')
        //     ->get()
        //     ->toArray();
    }

    /**
     * Ambil data lengkap dari KP_LBUT_DAILY
     */
    private function getLbutData()
    {
        return DB::connection('dwh_dynamic')
            ->select('SELECT * FROM DWHFA.dbo.KREDIT_PEMBIAYAAN_LBUT_DAILY');
    }

    /**
     * Cari data LBUT yang tidak ada di F01
     */
    private function findForNewData($f01Data, $lbutRekeningList)
    {
        $missingData = [];
        $lbutRekeningSet = array_flip($lbutRekeningList); // Convert to hash map for O(1) lookup

        foreach ($f01Data as $f01Record) {
            $nomorRekening = $f01Record->nomor_rekening_fasilitas;

            // Jika nomor rekening tidak ada di LBUT, tambahkan ke missing data
            if (!isset($lbutRekeningSet[$nomorRekening])) {
                $missingData[] = $f01Record;
            }
        }

        return $missingData;
    }

    /**
     * Cari data KP_LBUT_DAILY yang tidak ada di F01
     */
    private function findMissingLbutData($lbutData, $f01RekeningList)
    {
        $missingData = [];
        $f01RekeningSet = array_flip($f01RekeningList); // Convert to hash map for O(1) lookup

        foreach ($lbutData as $lbutRecord) {
            $nomorRekening = $lbutRecord->nomorRekening;

            // Jika nomor rekening tidak ada di F01, tambahkan ke missing data
            if (!isset($f01RekeningSet[$nomorRekening])) {
                $missingData[] = $lbutRecord;
            }
        }

        return $missingData;
    }

    /**
     * Tampilkan detail data F01 yang hilang
     */
    private function displayMissingF01Details($missingData)
    {
        $headers = ['No', 'Nomor Rekening', 'CIF Debitur', 'Jenis Kredit', 'Plafon', 'Baki Debet'];
        $rows = [];

        foreach (array_slice($missingData, 0, 10) as $index => $data) {
            $rows[] = [
                $index + 1,
                $data->nomor_rekening_fasilitas ?? '-',
                $data->nomor_cif_debitur ?? '-',
                $data->kode_jenis_kredit_pembiayaan ?? '-',
                number_format($data->plafon ?? 0, 0, ',', '.'),
                number_format($data->baki_debet ?? 0, 0, ',', '.')
            ];
        }

        $this->table($headers, $rows);

        if (count($missingData) > 10) {
            $this->info("... dan " . (count($missingData) - 10) . " record lainnya");
        }
    }

    /**
     * Tampilkan detail data KP_LBUT_DAILY yang hilang
     */
    private function displayMissingLbutDetails($missingData)
    {
        $headers = ['No', 'Nomor Rekening', 'ID Debitur', 'Jenis Kredit', 'Plafon', 'Baki Debet'];
        $rows = [];

        foreach (array_slice($missingData, 0, 10) as $index => $data) {
            $rows[] = [
                $index + 1,
                $data->nomorRekening ?? '-',
                $data->id_debitur ?? '-',
                $data->jenis_kredit_pembiayaan ?? '-',
                number_format($data->plafon ?? 0, 0, ',', '.'),
                number_format($data->baki_debet ?? 0, 0, ',', '.')
            ];
        }

        $this->table($headers, $rows);

        if (count($missingData) > 10) {
            $this->info("... dan " . (count($missingData) - 10) . " record lainnya");
        }
    }

    /**
     * Insert data F01 yang hilang ke kp_lbut_daily
     */
    private function insertMissingF01Data($missingData, $periode)
    {
        $insertedCount = 0;
        $batchSize = 1000;
        $batches = array_chunk($missingData, $batchSize);

        DB::beginTransaction();

        try {
            foreach ($batches as $batchIndex => $batch) {
                $insertData = [];

                foreach ($batch as $data) {
                    $insertData[] = [
                        'nomor_rekening' => $data->nomor_rekening_fasilitas,
                        'id_debitur' => $data->nomor_cif_debitur,
                        'jenis_kredit_pembiayaan' => $data->kode_jenis_kredit_pembiayaan,
                        'plafon' => $data->plafon,
                        'baki_debet' => $data->baki_debet,
                        'kualitas' => $data->kode_kolektibilitas,
                        'kode_cabang' => $data->kode_kantor_cabang,
                        'tanggal_jatuh_tempo' => $data->tanggal_jatuh_tempo,
                        'periode_data' => $periode,
                        'periode_laporan' => Carbon::now()->format('Y-m'),
                        'source_table' => 'F01',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                DB::table('kp_lbut_daily')->insert($insertData);
                $insertedCount += count($insertData);

                $this->info("  📦 F01 Batch " . ($batchIndex + 1) . " - " . count($insertData) . " records inserted");
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return $insertedCount;
    }

    /**
     * Insert data KP_LBUT_DAILY yang hilang ke kp_lbut_daily (untuk tracking/audit)
     */
    private function insertMissingLbutData($missingData, $periode)
    {
        $insertedCount = 0;
        $batchSize = 1000;
        $batches = array_chunk($missingData, $batchSize);

        DB::beginTransaction();

        try {
            foreach ($batches as $batchIndex => $batch) {
                $insertData = [];

                foreach ($batch as $data) {
                    $insertData[] = [
                        'nomor_rekening' => $data->nomor_rekening,
                        'id_debitur' => $data->id_debitur,
                        'jenis_kredit_pembiayaan' => $data->jenis_kredit_pembiayaan,
                        'plafon' => $data->plafon,
                        'baki_debet' => $data->baki_debet,
                        'kualitas' => $data->kualitas,
                        'kode_cabang' => $data->kode_cabang,
                        'tanggal_jatuh_tempo' => $data->tanggal_jatuh_tempo,
                        'periode_data' => $periode,
                        'periode_laporan' => Carbon::now()->format('Y-m'),
                        'source_table' => 'KP_LBUT_DAILY',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                DB::table('kp_lbut_daily')->insert($insertData);
                $insertedCount += count($insertData);

                $this->info("  📦 LBUT Batch " . ($batchIndex + 1) . " - " . count($insertData) . " records inserted");
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return $insertedCount;
    }
}