<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReportBatchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'institution_code' => $this->institution_code,
            'branch_code' => $this->branch_code,
            'report_code' => $this->report_code,
            'report_month' => $this->report_month,
            'report_year' => $this->report_year,
            'status' => $this->status,
            'record_count' => $this->record_count,
            'validation_results' => $this->validation_results,
            'error_message' => $this->error_message,
            'processed_at' => $this->processed_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Computed attributes
            'period_name' => $this->period_name,
            'status_label' => $this->status_label,
            'completion_percentage' => $this->completion_percentage,
            
            // Additional metadata when requested
            'previous_period' => $this->when(
                $request->has('include_periods'),
                fn() => $this->getPreviousPeriod()
            ),
            'comparison_period' => $this->when(
                $request->has('include_periods'),
                fn() => $this->getComparisonPeriod()
            ),
            'has_previous_period_data' => $this->when(
                $request->has('include_validation'),
                fn() => $this->hasPreviousPeriodData()
            ),
            'has_comparison_period_data' => $this->when(
                $request->has('include_validation'),
                fn() => $this->hasComparisonPeriodData()
            ),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'can_edit' => $this->canEdit(),
                'can_delete' => $this->canDelete(),
                'can_regenerate' => $this->canRegenerate(),
                'can_export' => $this->canExport(),
            ],
        ];
    }

    /**
     * Check if the report batch can be edited
     */
    private function canEdit(): bool
    {
        // Can edit if not completed or if user has admin privileges
        return !$this->isCompleted();
    }

    /**
     * Check if the report batch can be deleted
     */
    private function canDelete(): bool
    {
        // Can delete if not completed
        return !$this->isCompleted();
    }

    /**
     * Check if the report batch can be regenerated
     */
    private function canRegenerate(): bool
    {
        // Can regenerate if failed or if user wants to overwrite
        return $this->hasFailed() || !$this->isProcessing();
    }

    /**
     * Check if the report batch can be exported
     */
    private function canExport(): bool
    {
        // Can export if completed and has data
        return $this->isCompleted() && $this->record_count > 0;
    }
}
