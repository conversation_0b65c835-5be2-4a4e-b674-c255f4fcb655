<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ImportReportV2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-report-v2 {type : Type of report to import} {file : Path to the csv file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import report from csv file,';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
    }
}
