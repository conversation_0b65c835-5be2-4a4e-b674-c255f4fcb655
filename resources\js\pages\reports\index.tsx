import { Head, useForm } from '@inertiajs/react';
import { CalendarIcon, FilePlus, FileText, Filter, Plus, X } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { useState } from 'react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Reports',
        href: '/reports',
    },
];

interface ReportTemplate {
    id: number;
    name: string;
    alias: string;
    description: string;
}

interface Report {
    id: number | null;
    name: string;
    report_template_id: number;
    template: ReportTemplate;
    month: number;
    year: number;
    status: string;
    exists?: boolean;
    created_at?: string;
    submitted_at?: string;
}

interface MonthReports {
    month: number;
    month_name: string;
    reports: Report[];
    existing_reports_count: number;
    total_templates: number;
    is_current_month: boolean;
}

interface Props {
    reportsByMonth: Record<number, MonthReports>;
    currentYear: number;
    availableYears: number[];
}

export default function ReportsIndex({ reportsByMonth, currentYear, availableYears }: Props) {
    const [isFilterOpen, setIsFilterOpen] = useState(false);

    const { data, setData, get, processing } = useForm<{year: string}>({
        year: currentYear.toString(),
    });

    const applyFilters = () => {
        setIsFilterOpen(false);
        get(route('reports.index', { year: data.year }));
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'submitted':
                return (
                    <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Submitted
                    </Badge>
                );
            case 'draft':
                return (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        Draft
                    </Badge>
                );
            case 'not_created':
                return (
                    <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                        Not Created
                    </Badge>
                );
            default:
                return (
                    <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                        {status}
                    </Badge>
                );
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Reports" />

            <div className="flex flex-col gap-6 p-4">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div className="flex items-center gap-2">
                        <h1 className="text-2xl font-bold">Reports</h1>
                        <Badge variant="outline" className="ml-2">
                            <CalendarIcon className="h-3 w-3 mr-1" />
                            {currentYear}
                        </Badge>
                    </div>
                    <div className="flex items-center gap-2 self-end sm:self-auto">
                        <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                            <PopoverTrigger asChild>
                                <Button variant="outline">
                                    <Filter className="mr-2 h-4 w-4" />
                                    Year
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-80">
                                <div className="grid gap-4">
                                    <div className="flex items-center justify-between">
                                        <h4 className="font-medium">Select Year</h4>
                                    </div>
                                    <Separator />
                                    <div className="grid gap-2">
                                        <Label htmlFor="year">Year</Label>
                                        <Select
                                            value={data.year}
                                            onValueChange={(value) => setData('year', value)}
                                        >
                                            <SelectTrigger id="year">
                                                <SelectValue placeholder="Select year" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {availableYears.map((year) => (
                                                    <SelectItem key={year} value={year.toString()}>
                                                        {year}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <Button onClick={applyFilters} disabled={processing}>Apply</Button>
                                </div>
                            </PopoverContent>
                        </Popover>

                        <Button asChild>
                            <Link href={route('reports.create')}>
                                <FilePlus className="mr-2 h-4 w-4" />
                                Create Report
                            </Link>
                        </Button>
                    </div>
                </div>

                <Accordion
                    type="multiple"
                    defaultValue={Object.values(reportsByMonth)
                        .filter(month => month.is_current_month)
                        .map(month => month.month.toString())}
                >
                    {Object.values(reportsByMonth).map((monthData) => (
                        <AccordionItem key={monthData.month} value={monthData.month.toString()}>
                            <AccordionTrigger className="px-4">
                                <div className="flex items-center">
                                    <span className="font-medium">{monthData.month_name}</span>
                                    <Badge className="ml-2" variant="outline">
                                        {`${monthData.existing_reports_count} of ${monthData.total_templates} reports`}
                                    </Badge>
                                    {monthData.is_current_month && (
                                        <Badge className="ml-2" variant="secondary">Current</Badge>
                                    )}
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>
                                <div className="grid gap-4 p-4 md:grid-cols-2 lg:grid-cols-3">
                                    {monthData.reports.map((report) => (
                                        <Card key={`${report.report_template_id}-${monthData.month}`}>
                                            <CardHeader>
                                                <CardTitle>{report.name}</CardTitle>
                                                <CardDescription>
                                                    {report.template.alias} - {report.template.description.substring(0, 60)}
                                                    {report.template.description.length > 60 ? '...' : ''}
                                                </CardDescription>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="flex justify-between items-center">
                                                    <div className="text-sm text-muted-foreground">
                                                        {getStatusBadge(report.status)}
                                                    </div>
                                                    {report.id ? (
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('reports.show', { report: report.id })}>
                                                                <FileText className="mr-2 h-4 w-4" />
                                                                View
                                                            </Link>
                                                        </Button>
                                                    ) : (
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('reports.create', {
                                                                template_id: report.report_template_id,
                                                                month: monthData.month,
                                                                year: currentYear
                                                            })}>
                                                                <Plus className="mr-2 h-4 w-4" />
                                                                Create
                                                            </Link>
                                                        </Button>
                                                    )}
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </AccordionContent>
                        </AccordionItem>
                    ))}
                </Accordion>
            </div>
        </AppLayout>
    );
}
