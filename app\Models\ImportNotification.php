<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class ImportNotification extends Model
{
    protected $fillable = [
        'filename',
        'report_code',
        'report_month',
        'report_year',
        'records_imported',
        'status',
        'error_message',
        'import_details',
    ];

    protected $casts = [
        'report_month' => 'integer',
        'report_year' => 'integer',
        'records_imported' => 'integer',
        'import_details' => 'array',
    ];

    protected $appends = [
        'period_name',
        'status_label',
    ];

    /**
     * Get formatted period name
     */
    public function getPeriodNameAttribute(): string
    {
        return sprintf('%02d/%d', $this->report_month, $this->report_year);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'success' => 'Success',
            'failed' => 'Failed',
            default => 'Unknown',
        };
    }

    /**
     * Scope for successful imports
     */
    public function scopeSuccessful(Builder $query): Builder
    {
        return $query->where('status', 'success');
    }

    /**
     * Scope for failed imports
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for specific report code
     */
    public function scopeForReportCode(Builder $query, string $reportCode): Builder
    {
        return $query->where('report_code', $reportCode);
    }

    /**
     * Scope for specific period
     */
    public function scopeForPeriod(Builder $query, int $year, int $month): Builder
    {
        return $query->where('report_year', $year)
                    ->where('report_month', $month);
    }

    /**
     * Scope for recent imports
     */
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
