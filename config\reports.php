<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Report Types
    |--------------------------------------------------------------------------
    |
    | Mapping tipe report ke class yang mengimplementasikan ReportStrategy.
    | Kunci adalah tipe report dan nilai adalah nama class lengkap.
    |
    */
    'types' => [
        'A01' => \App\Reports\Types\A01Report::class,
        'D01' => \App\Reports\Types\D01Report::class,
        'D02' => \App\Reports\Types\D02Report::class,
        'F01' => \App\Reports\Types\F01Report::class,
        'F03' => \App\Reports\Types\F03Report::class,
        'F05' => \App\Reports\Types\F05Report::class,
        'P01' => \App\Reports\Types\P01Report::class,
        // Tambahkan tipe report lainnya di sini
    ],
];
