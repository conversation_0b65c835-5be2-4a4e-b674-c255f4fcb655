<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Storage;

class ImportReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'filename' => [
                'required',
                'string',
                'regex:/^[A-Z]\d{2}_\d{6}\.csv$/i', // Match format: A01_042025.csv
                function ($attribute, $value, $fail) {
                    $filePath = 'seed/' . $value;
                    if (!Storage::exists($filePath)) {
                        $fail("The file {$value} does not exist in the seed directory.");
                    }
                },
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'filename.required' => 'A filename is required.',
            'filename.regex' => 'The filename must follow the format: {report_code}_{month}{year}.csv (e.g., A01_042025.csv)',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'filename' => 'file name',
        ];
    }
}
