<?php

namespace Tests\Feature;

use App\Models\ImportNotification;
use App\Models\ReportBatch;
use App\Models\Reports\A01;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ImportControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test CSV file
        Storage::fake('local');
        $this->createTestCsvFile();
    }

    public function test_import_files_endpoint_returns_available_files()
    {
        $response = $this->getJson('/import/files');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'filename',
                            'path',
                            'report_code',
                            'month',
                            'year',
                            'period_name',
                            'size',
                            'last_modified',
                        ]
                    ]
                ]);
    }

    public function test_import_preview_endpoint_returns_csv_preview()
    {
        $response = $this->postJson('/import/preview', [
            'filename' => 'A01_042025.csv'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'headers',
                        'preview_rows',
                        'total_rows',
                        'separator',
                    ]
                ]);
    }

    public function test_import_file_endpoint_imports_csv_successfully()
    {
        // Ensure no existing data
        $this->assertEquals(0, ReportBatch::count());
        $this->assertEquals(0, A01::count());
        $this->assertEquals(0, ImportNotification::count());

        $response = $this->postJson('/import/file', [
            'filename' => 'A01_042025.csv'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'notification_id',
                        'records_imported',
                        'report_code',
                        'period',
                        'processing_time',
                    ]
                ]);

        // Verify data was imported
        $this->assertEquals(1, ReportBatch::count());
        $this->assertEquals(2, A01::count());
        $this->assertEquals(1, ImportNotification::count());

        $notification = ImportNotification::first();
        $this->assertEquals('success', $notification->status);
        $this->assertEquals(2, $notification->records_imported);
    }

    public function test_import_history_endpoint_returns_import_notifications()
    {
        // Create some test notifications
        ImportNotification::create([
            'filename' => 'A01_042025.csv',
            'report_code' => 'A01',
            'report_month' => 4,
            'report_year' => 2025,
            'records_imported' => 100,
            'status' => 'success',
        ]);

        $response = $this->getJson('/import/history');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'filename',
                                'report_code',
                                'report_month',
                                'report_year',
                                'records_imported',
                                'status',
                                'created_at',
                                'updated_at',
                                'period_name',
                                'status_label',
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total',
                    ]
                ]);
    }

    public function test_import_file_validation_fails_for_invalid_filename()
    {
        $response = $this->postJson('/import/file', [
            'filename' => 'invalid_filename.csv'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['filename']);
    }

    public function test_import_file_validation_fails_for_nonexistent_file()
    {
        $response = $this->postJson('/import/file', [
            'filename' => 'A01_999999.csv'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['filename']);
    }

    private function createTestCsvFile()
    {
        $csvContent = "FLAG DETAIL;KODE REGISTER / NOMOR AGUNAN;NOMOR REKENING FASILITAS;NOMOR CIF DEBITUR;KODE JENIS SEGMEN FASILITAS;KODE STATUS AGUNAN;KODE JENIS AGUNAN;PERINGKAT AGUNAN;KODE LEMBAGA PEMERINGKAT;KODE JENIS PENGIKATAN;TANGGAL PENGIKATAN;NAMA PEMILIK AGUNAN;BUKTI KEPEMILIKAN;ALAMAT AGUNAN;KODE KAB/KOTA (DATI 2) LOKASI AGUNAN;NILAI AGUNAN SESUAI NJOP;NILAI AGUNAN MENURUT LJK;TANGGAL PENILAIAN LJK;NILAI AGUNAN PENILAI INDEPENDEN;NAMA PENILAI INDEPENDEN;TANGGAL PENILAIAN PENILAI INDEPENDEN;STATUS PARIPASU;PERSENTASE PARIPASU;STATUS KREDIT JOIN;DIASURANSIKAN;KETERANGAN;KODE KANTOR CABANG;OPERASI DATA\n";
        $csvContent .= "D;86086000008880001;F0000508533;8600000888;F01;1;AN0299;;;99;20230309;HASANI;SPPH A,N HASANI;JL, MUTIARA MARDIKA RIJALI SIRIMAU;8191;25000000;25000000;20230309;;;;T;;T;T;;008;U\n";
        $csvContent .= "D;86086000003810001;F0000494207;8600000381;F01;1;AN0299;;;99;20220915;ARAS ARTAN;SPPH A,N ARAS ARTAN;DEBOWAE, RT 000 RW 000, KEL KEC KAB PROVINSI MALUKU DEBOWAY, WAIAPO,;8104;50000000;50000000;20220915;;;;T;;T;Y;;008;U\n";
        
        Storage::put('seed/A01_042025.csv', $csvContent);
    }
}
