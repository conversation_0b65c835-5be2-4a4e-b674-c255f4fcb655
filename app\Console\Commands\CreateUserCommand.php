<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules;

class CreateUserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create 
                            {--name= : The name of the user}
                            {--email= : The email of the user}
                            {--password= : The password of the user}
                            {--verify : Verify the user email immediately}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->option('name');
        $email = $this->option('email');
        $password = $this->option('password');
        $verify = $this->option('verify');

        // If options are not provided, prompt for them
        if (! $name) {
            $name = $this->ask('What is the user name?');
        }

        if (! $email) {
            $email = $this->ask('What is the user email?');
        }

        if (! $password) {
            $password = $this->secret('What is the user password?');
            $passwordConfirmation = $this->secret('Confirm the password');

            if ($password !== $passwordConfirmation) {
                $this->error('Passwords do not match!');

                return 1;
            }
        }

        // Validate the input
        $validator = Validator::make([
            'name' => $name,
            'email' => $email,
            'password' => $password,
        ], [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', Rules\Password::defaults()],
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }

            return 1;
        }

        // Create the user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
        ]);

        // Verify email if requested
        if ($verify) {
            $user->email_verified_at = now();
            $user->save();
            $this->info('User email has been verified.');
        }

        $this->info('User created successfully!');
        $this->table(
            ['ID', 'Name', 'Email', 'Verified'],
            [[$user->id, $user->name, $user->email, $user->email_verified_at ? 'Yes' : 'No']]
        );

        return 0;
    }
}
