<?php

/**
 * Test Laravel SQL Server Connection
 */

// Bootstrap <PERSON>vel
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

echo "Testing Laravel SQL Server Connection\n";
echo "------------------------------------\n\n";

// Test dwh_dynamic connection
echo "Testing 'dwh_dynamic' connection:\n";

// Try different database names
$databases = [
    // env('DB_NAME_PREFIX_DWH'), // Just the prefix
    // env('DB_NAME_PREFIX_DWH') . '_2023', // Example year
    // env('DB_NAME_PREFIX_DWH') . '_2022', // Example year
    // 'master' // Default system database
    env('DB_NAME_PREFIX_DWH').'_2025', // Example year and month
];

$connected = false;

foreach ($databases as $database) {
    echo "Trying database: $database\n";
    Config::set('database.connections.dwh_dynamic.database', $database);
    DB::purge('dwh_dynamic'); // Clear the connection cache

    try {
        // Test the connection
        $result = DB::connection('dwh_dynamic')->select('SELECT @@VERSION as version');

        echo "✅ Connection successful with database: $database\n";
        echo 'SQL Server Version: '.$result[0]->version."\n\n";

        $connected = true;
        break; // Exit the loop if connection is successful
    } catch (\Exception $e) {
        echo "❌ Connection failed with database: $database\n";
        echo 'Error: '.$e->getMessage()."\n\n";
    }
}

// Show final connection configuration
echo "Final Connection Configuration:\n";
$config = Config::get('database.connections.dwh_dynamic');
echo 'Driver: '.$config['driver']."\n";
echo 'Host: '.$config['host']."\n";
echo 'Port: '.$config['port']."\n";
echo 'Database: '.$config['database']."\n";
echo 'Username: '.$config['username']."\n";
echo 'Password: '.str_repeat('*', strlen($config['password']))."\n";
echo 'Trust Server Certificate: '.($config['trust_server_certificate'] ? 'Yes' : 'No')."\n";
echo 'Encrypt: '.($config['encrypt'] ? 'Yes' : 'No')."\n";

// Summary
if ($connected) {
    echo "\n✅ Connection to SQL Server was successful!\n";
    echo "You can now use the 'dwh_dynamic' connection in your Laravel application.\n";
} else {
    echo "\n❌ Could not connect to any of the tried databases.\n";
    echo "Please check your database credentials and make sure the SQL Server is accessible.\n";
}

echo "\nTest completed.\n";
