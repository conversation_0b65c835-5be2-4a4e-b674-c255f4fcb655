<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ReportBatchCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'last_page' => $this->lastPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'summary' => $this->getSummary(),
        ];
    }

    /**
     * Get summary statistics for the collection
     */
    private function getSummary(): array
    {
        $items = $this->collection;
        
        $summary = [
            'total_records' => $items->sum('record_count'),
            'status_breakdown' => [
                'pending' => $items->where('status', 'pending')->count(),
                'processing' => $items->where('status', 'processing')->count(),
                'completed' => $items->where('status', 'completed')->count(),
                'failed' => $items->where('status', 'failed')->count(),
            ],
            'report_code_breakdown' => [],
            'completion_rate' => 0,
        ];

        // Calculate completion rate
        $total = $items->count();
        if ($total > 0) {
            $completed = $summary['status_breakdown']['completed'];
            $summary['completion_rate'] = round(($completed / $total) * 100, 2);
        }

        // Group by report code
        $reportCodes = $items->groupBy('report_code');
        foreach ($reportCodes as $code => $batches) {
            $summary['report_code_breakdown'][$code] = [
                'total' => $batches->count(),
                'completed' => $batches->where('status', 'completed')->count(),
                'total_records' => $batches->sum('record_count'),
            ];
        }

        return $summary;
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'filters_applied' => $request->only([
                'year', 'month', 'report_code', 'status', 
                'institution_code', 'branch_code', 'search'
            ]),
            'available_filters' => [
                'report_codes' => array_keys(config('report-mapping', [])),
                'years' => range(2020, (int) date('Y')),
                'months' => range(1, 12),
                'statuses' => ['pending', 'processing', 'completed', 'failed'],
            ],
        ];
    }
}
