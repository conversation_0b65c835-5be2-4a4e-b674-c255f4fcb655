<?php

namespace App\Console\Commands;

use App\Models\ReportBatch;
use App\Services\ReportBatchService;
use Illuminate\Console\Command;

class TestReportBatch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-report-batch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the report batch management system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Report Batch Management System...');

        try {
            // Test creating a report batch
            $batch = ReportBatch::create([
                'institution_code' => 'TEST001',
                'branch_code' => 'BR001',
                'report_code' => 'A01',
                'report_month' => 11,
                'report_year' => 2024,
                'status' => 'pending'
            ]);

            $this->info("✅ Created report batch with ID: {$batch->id}");
            $this->info("📅 Period name: {$batch->period_name}");
            $this->info("📊 Status: {$batch->status_label}");
            $this->info("📈 Completion: {$batch->completion_percentage}%");

            // Test the service
            $service = new ReportBatchService();
            $stats = $service->getDashboardStatistics(2024);
            $this->info("📋 Total batches: {$stats['total_batches']}");
            $this->info("✅ Completed batches: {$stats['completed_batches']}");
            $this->info("⏳ Pending batches: {$stats['pending_batches']}");
            $this->info("❌ Failed batches: {$stats['failed_batches']}");
            $this->info("📊 Completion rate: {$stats['completion_rate']}%");

            // Test validation
            $validation = $service->validateDataAvailability($batch);
            $this->info("🔍 Validation valid: " . ($validation['valid'] ? 'Yes' : 'No'));
            $this->info("⚠️  Warnings: " . count($validation['warnings']));
            $this->info("❌ Errors: " . count($validation['errors']));

            // Test period calculations
            $previous = $batch->getPreviousPeriod();
            $comparison = $batch->getComparisonPeriod();
            $this->info("📅 Previous period: {$previous['year']}-{$previous['month']}");
            $this->info("📅 Comparison period: {$comparison['year']}-{$comparison['month']}");

            // Clean up
            $batch->delete();
            $this->info("🧹 Cleaned up test data");

            $this->info("\n✅ All tests passed! Report batch management system is working correctly.");

        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            $this->error("📍 File: " . $e->getFile() . ":" . $e->getLine());
            return 1;
        }

        return 0;
    }
}
