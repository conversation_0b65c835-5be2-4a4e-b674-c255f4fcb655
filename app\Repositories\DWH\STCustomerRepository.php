<?php

namespace App\Repositories\DWH;

use App\Models\CustomerDynamic;
use App\Models\DWH\STCustomer;

class STCustomerRepository
{
    protected STCustomer $model;

    public function __construct(STCustomer $model)
    {
        $this->model = $model;
    }

    /**
     * Get customer by CIF number.
     *
     * @param  string|null  $period  Format YYYYMM
     * @return CustomerDynamic|null
     */
    public function findByCif(string $cif, ?string $period = null): ?STCustomer
    {
        $model = $this->model->setPeriod($period);

        return $model->where('CUSTOMER_NO', $cif)->first();
    }

    /**
     * List customers
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function all(?string $period = null)
    {
        $model = (new STCustomer)->setPeriod($period);

        dd($model->getConnection());

        return $model->getConnection();
    }

    public function findManyByCif(array $cifs)
    {
        $model = (new STCustomer)->setPeriod();

        return $model->select('CUSTOMER_NO', 'LEGAL_DOC_NAME', 'LEGAL_ID', 'NAME_1', 'NAME_1', 'GENDER', 'PLACE_OF_BIRTH', 'DATE_OF_BIRTH', 'CUS_NPWP', 'ADDRESS', 'KTP_KELURAHAN', 'KTP_KECAMATAN', 'SID_DATI2DEBTOR', 'POST_CODE', 'PHONE_1', 'SMS_1', 'EMAIL_1', 'NATIONALITY', 'OCCUPATION', 'EMPLOYERS_NAME', 'SID_JENIS_USAHA', 'EMPLOYERS_ADD', 'LAST_EDUCATION', 'KYC_INCOM_RNG', 'KYC_INCOME_SRC', 'NO_OF_DEPENDENTS', 'SID_HUB_BANK', 'LBU_GOL_DEB', 'MARITAL_STATUS', 'SPOUSE_ID', 'SPOUSE_NAME', 'SPOU_DT_OF_BIRT', 'L_PISAH_HARTA', 'SID_MELANGGAR', 'SID_MELAMPAUI', 'MOTHER_MAID_NAM')->whereIn('CUSTOMER_NO', $cifs)->limit(31)->get();
    }

    public function findManyByPeriodeDay(?string $periodeDay, ?int $page, ?int $perPage)
    {
        $model = (new STCustomer)->setPeriod();

        $period = now()->subDay()->format('Ymd');

        $year = substr($period, 0, 4);
        $month = substr($period, 4, 2);
        $day = substr($period, 6, 2);

        $page = $page ?? 1;
        $perPage = $perPage ?? 10;

        $offset = ($page - 1) * $perPage;

        // $query = $model->select('CUSTOMER_NO', 'LEGAL_DOC_NAME', 'LEGAL_ID', 'NAME_1', 'NAME_1', 'GENDER', 'PLACE_OF_BIRTH', 'DATE_OF_BIRTH', 'CUS_NPWP', 'ADDRESS', 'KTP_KELURAHAN', 'KTP_KECAMATAN', 'SID_DATI2DEBTOR', 'POST_CODE', 'PHONE_1', 'SMS_1', 'EMAIL_1', 'NATIONALITY', 'OCCUPATION', 'EMPLOYERS_NAME', 'SID_JENIS_USAHA', 'EMPLOYERS_ADD', 'LAST_EDUCATION', 'KYC_INCOM_RNG', 'KYC_INCOME_SRC', 'NO_OF_DEPENDENTS', 'SID_HUB_BANK', 'LBU_GOL_DEB', 'MARITAL_STATUS', 'SPOUSE_ID', 'SPOUSE_NAME', 'SPOU_DT_OF_BIRT', 'L_PISAH_HARTA', 'SID_MELANGGAR', 'SID_MELAMPAUI', 'MOTHER_MAID_NAM')->where('PERIODE', $period);
        $query = $model->select('*');
        $total = $query->count();

        $items = $query->offset($offset)->limit(1)->get();

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
            ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()]
        );
    }

    public function getCustomersForReportByEndPeriode(string $periode): array
    {
        $model = (new STCustomer)->setPeriod($periode);

        $model->where('PERIODE', $periode);

        $result =  $model->limit(1)->get();

        return $result->toArray();
    }
}
