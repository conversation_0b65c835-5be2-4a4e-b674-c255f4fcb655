<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_batches', function (Blueprint $table) {
            $table->id();
            $table->string('institution_code', 10);
            $table->string('branch_code', 10);
            $table->string('report_code', 10);
            $table->unsignedTinyInteger('report_month');
            $table->unsignedSmallInteger('report_year');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->integer('record_count')->default(0);
            $table->json('validation_results')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            // Add indexes for performance
            $table->index(['report_year', 'report_month']);
            $table->index(['institution_code', 'branch_code']);
            $table->index('report_code');
            $table->index('status');
            $table->unique(['institution_code', 'branch_code', 'report_code', 'report_month', 'report_year'], 'unique_report_batch');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_batches');
    }
};
