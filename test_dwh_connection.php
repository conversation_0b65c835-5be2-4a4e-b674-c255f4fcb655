<?php

/**
 * Test connection to DWH SQL Server
 */

// Configuration from .env
$serverName = '*************';
$port = '1433';
$username = 'it-h2h';
$password = 'gtiP@ssw0rd';
$databaseName = 'T24DWHSTAGING'; // Using the prefix as the database name for testing

echo "Testing connection to SQL Server:\n";
echo "Server: $serverName:$port\n";
echo "Database: $databaseName\n";
echo "Username: $username\n";
echo 'Password: '.str_repeat('*', strlen($password))."\n\n";

// Check if extensions are loaded
echo "Checking required extensions:\n";
if (extension_loaded('sqlsrv')) {
    echo "✅ sqlsrv extension is loaded.\n";
    $sqlsrv_loaded = true;
} else {
    echo "❌ sqlsrv extension is NOT loaded.\n";
    $sqlsrv_loaded = false;
}

if (extension_loaded('pdo_sqlsrv')) {
    echo "✅ pdo_sqlsrv extension is loaded.\n";
    $pdo_sqlsrv_loaded = true;
} else {
    echo "❌ pdo_sqlsrv extension is NOT loaded.\n";
    $pdo_sqlsrv_loaded = false;
}
echo "\n";

// Test connection using sqlsrv extension
if ($sqlsrv_loaded) {
    echo "Testing connection using sqlsrv extension:\n";
    try {
        // Connection options
        $connectionOptions = [
            'Database' => $databaseName,
            'Uid' => $username,
            'PWD' => $password,
            // Optional settings that might help with connection issues
            'TrustServerCertificate' => true,
            'Encrypt' => true,
            'ConnectRetryCount' => 3,
            'LoginTimeout' => 30,
        ];

        // Establish the connection
        $conn = sqlsrv_connect("$serverName,$port", $connectionOptions);

        if ($conn === false) {
            echo "❌ Connection failed using sqlsrv extension.\n";
            echo '   Error: '.print_r(sqlsrv_errors(), true)."\n";
        } else {
            echo "✅ Connection successful using sqlsrv extension!\n";

            // Test a simple query
            $sql = 'SELECT @@VERSION as SQL_Server_Version';
            $stmt = sqlsrv_query($conn, $sql);

            if ($stmt === false) {
                echo '❌ Query failed: '.print_r(sqlsrv_errors(), true)."\n";
            } else {
                if (sqlsrv_fetch($stmt)) {
                    $version = sqlsrv_get_field($stmt, 0);
                    echo "   SQL Server Version: $version\n";
                }
                sqlsrv_free_stmt($stmt);
            }

            // Close the connection
            sqlsrv_close($conn);
        }
    } catch (Exception $e) {
        echo '❌ Exception: '.$e->getMessage()."\n";
    }
    echo "\n";
}

// Test connection using PDO_SQLSRV extension
if ($pdo_sqlsrv_loaded) {
    echo "Testing connection using PDO_SQLSRV extension:\n";
    try {
        // Create connection string with port and trust server certificate
        $dsn = "sqlsrv:Server=$serverName,$port;Database=$databaseName;TrustServerCertificate=true;Encrypt=true";

        // Connection options
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ];

        // Create a PDO instance
        $pdo = new PDO($dsn, $username, $password, $options);

        echo "✅ Connection successful using PDO_SQLSRV extension!\n";

        // Test a simple query
        $stmt = $pdo->query('SELECT @@VERSION as SQL_Server_Version');
        $row = $stmt->fetch();
        echo '   SQL Server Version: '.$row['SQL_Server_Version']."\n";

        // Close the connection
        $pdo = null;
    } catch (PDOException $e) {
        echo "❌ Connection failed using PDO_SQLSRV extension.\n";
        echo '   Error: '.$e->getMessage()."\n";
    }
    echo "\n";
}

echo "Test completed.\n";
