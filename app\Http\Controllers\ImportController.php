<?php

namespace App\Http\Controllers;

use App\Http\Requests\ImportReportRequest;
use App\Services\ReportImportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

class ImportController extends Controller
{
    private ReportImportService $importService;

    public function __construct(ReportImportService $importService)
    {
        $this->importService = $importService;
    }

    /**
     * Display the import page with available files
     */
    public function index(): InertiaResponse
    {
        $availableFiles = $this->importService->getAvailableCsvFiles();
        $recentImports = $this->importService->getImportHistory([], 10);

        return Inertia::render('Import/Index', [
            'availableFiles' => $availableFiles,
            'recentImports' => $recentImports,
        ]);
    }

    /**
     * Import a specific CSV file
     */
    public function import(ImportReportRequest $request): JsonResponse
    {
        try {
            $filename = $request->validated('filename');
            $filePath = 'seed/' . $filename;

            // Perform the import
            $notification = $this->importService->importFromCsv($filePath);

            if ($notification->status === 'success') {
                return response()->json([
                    'success' => true,
                    'message' => "Successfully imported {$notification->records_imported} records from {$filename}",
                    'data' => [
                        'notification_id' => $notification->id,
                        'records_imported' => $notification->records_imported,
                        'report_code' => $notification->report_code,
                        'period' => $notification->period_name,
                        'processing_time' => $notification->import_details['processing_time_ms'] ?? 0,
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => "Import failed: {$notification->error_message}",
                    'data' => [
                        'notification_id' => $notification->id,
                        'error_message' => $notification->error_message,
                    ],
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('Import controller error', [
                'filename' => $request->validated('filename'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred during import',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get import history with filters
     */
    public function history(Request $request): JsonResponse
    {
        $filters = $request->only(['report_code', 'status', 'year', 'month']);
        $perPage = $request->get('per_page', 15);

        $history = $this->importService->getImportHistory($filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $history,
        ]);
    }

    /**
     * Get available CSV files
     */
    public function files(): JsonResponse
    {
        $files = $this->importService->getAvailableCsvFiles();

        return response()->json([
            'success' => true,
            'data' => $files,
        ]);
    }

    /**
     * Preview CSV file content (first few rows)
     */
    public function preview(Request $request): JsonResponse
    {
        $request->validate([
            'filename' => 'required|string',
        ]);

        try {
            $filename = $request->get('filename');
            $filePath = 'seed/' . $filename;

            $csvService = app(\App\Services\CsvImportService::class);
            $csvData = $csvService->parseCsv($filePath);

            // Return only first 5 rows for preview
            $previewData = array_slice($csvData['data'], 0, 5);

            return response()->json([
                'success' => true,
                'data' => [
                    'headers' => $csvData['headers'],
                    'preview_rows' => $previewData,
                    'total_rows' => $csvData['total_rows'],
                    'separator' => $csvData['separator'],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to preview file',
                'error' => $e->getMessage(),
            ], 422);
        }
    }
}
