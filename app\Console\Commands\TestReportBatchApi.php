<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestReportBatchApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-report-batch-api {--host=http://localhost:8000 : The host to test against}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Report Batch API endpoints';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $host = $this->option('host');
        $this->info("🧪 Testing Report Batch API at {$host}...");

        try {
            // Test 1: Get all report batches
            $this->info("\n1️⃣ Testing GET /report");
            $response = Http::acceptJson()->get("{$host}/report");
            
            if ($response->successful()) {
                $data = $response->json();
                $this->info("✅ Success: Found {$data['meta']['total']} report batches");
                $this->info("📊 Completion rate: {$data['summary']['completion_rate']}%");
            } else {
                $this->error("❌ Failed: " . $response->status());
                return 1;
            }

            // Test 2: Get report batches with filters
            $this->info("\n2️⃣ Testing GET /report with filters");
            $response = Http::acceptJson()->get("{$host}/report", [
                'year' => 2024,
                'status' => 'completed',
                'per_page' => 5
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                $this->info("✅ Success: Found {$data['meta']['count']} completed batches for 2024");
            } else {
                $this->error("❌ Failed: " . $response->status());
                return 1;
            }

            // Test 3: Create a new report batch
            $this->info("\n3️⃣ Testing POST /report");
            $createData = [
                'institution_code' => 'TEST001',
                'branch_code' => 'BR001',
                'report_code' => 'A01',
                'report_month' => 1,
                'report_year' => 2023,
            ];

            $response = Http::acceptJson()->post("{$host}/report", $createData);
            
            if ($response->successful()) {
                $data = $response->json();
                $batchId = $data['data']['id'];
                $this->info("✅ Success: Created report batch with ID {$batchId}");
                
                // Test 4: Get the created report batch
                $this->info("\n4️⃣ Testing GET /report/{$batchId}");
                $response = Http::acceptJson()->get("{$host}/report/{$batchId}");
                
                if ($response->successful()) {
                    $this->info("✅ Success: Retrieved report batch details");
                } else {
                    $this->error("❌ Failed to retrieve batch: " . $response->status());
                }

                // Test 5: Validate data for the batch
                $this->info("\n5️⃣ Testing POST /report/{$batchId}/validate-data");
                $response = Http::acceptJson()->post("{$host}/report/{$batchId}/validate-data");
                
                if ($response->successful()) {
                    $data = $response->json();
                    $valid = $data['validation_results']['valid'] ? 'Valid' : 'Invalid';
                    $this->info("✅ Success: Data validation completed - {$valid}");
                } else {
                    $this->error("❌ Failed to validate data: " . $response->status());
                }

                // Test 6: Update the report batch
                $this->info("\n6️⃣ Testing PUT /report/{$batchId}");
                $updateData = [
                    'status' => 'completed',
                    'record_count' => 1000,
                ];

                $response = Http::acceptJson()->put("{$host}/report/{$batchId}", $updateData);
                
                if ($response->successful()) {
                    $this->info("✅ Success: Updated report batch status to completed");
                } else {
                    $this->error("❌ Failed to update batch: " . $response->status());
                }

                // Test 7: Export JSON
                $this->info("\n7️⃣ Testing GET /report/{$batchId}/export/json");
                $response = Http::acceptJson()->get("{$host}/report/{$batchId}/export/json");
                
                if ($response->successful()) {
                    $this->info("✅ Success: JSON export completed");
                } else {
                    $this->error("❌ Failed to export JSON: " . $response->status());
                }

                // Test 8: Delete the test batch
                $this->info("\n8️⃣ Testing DELETE /report/{$batchId}");
                $response = Http::acceptJson()->delete("{$host}/report/{$batchId}");
                
                if ($response->successful()) {
                    $this->info("✅ Success: Deleted test report batch");
                } else {
                    $this->error("❌ Failed to delete batch: " . $response->status());
                }

            } else {
                $this->error("❌ Failed to create batch: " . $response->status());
                if ($response->json()) {
                    $this->error("Error: " . json_encode($response->json(), JSON_PRETTY_PRINT));
                }
                return 1;
            }

            // Test 9: Dashboard statistics
            $this->info("\n9️⃣ Testing GET /report/dashboard");
            $response = Http::acceptJson()->get("{$host}/report/dashboard");
            
            if ($response->successful()) {
                $this->info("✅ Success: Dashboard data retrieved");
            } else {
                $this->error("❌ Failed to get dashboard: " . $response->status());
            }

            // Test 10: Bulk export
            $this->info("\n🔟 Testing POST /report-bulk/export");
            $bulkData = [
                'report_batch_ids' => [1, 2, 3], // Use existing IDs
                'format' => 'json'
            ];

            $response = Http::acceptJson()->post("{$host}/report-bulk/export", $bulkData);
            
            if ($response->successful()) {
                $this->info("✅ Success: Bulk export initiated");
            } else {
                $this->error("❌ Failed bulk export: " . $response->status());
            }

            $this->info("\n🎉 All API tests completed successfully!");

        } catch (\Exception $e) {
            $this->error("❌ Error during testing: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
