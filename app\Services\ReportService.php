<?php

/**
 * ReportService
 *
 * Service untuk mengelola laporan dengan pendekatan hybrid.
 *
 * Update 2025-05-15:
 * - Perbaikan pada proses mapping data dari file ke field
 * - Memastikan urutan data sesuai dengan urutan field yang didefinisikan
 * - Menyesuaikan posisi field (1-based) ke indeks array (0-based) dengan mengurangi 1
 * - Menambahkan validasi untuk mencegah indeks negatif atau di luar batas
 */

namespace App\Services;

use App\Models\Report;
use App\Models\ReportData;
use App\Models\ReportType;
use App\Models\ReportType as ModelsReportType;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ReportService
{
    /**
     * Parse report file name to extract components
     */
    public function parseReportFilename(string $filename): array
    {
        // Remove file extension
        $filename = pathinfo($filename, PATHINFO_FILENAME);

        // Split by dots
        $parts = explode('.', $filename);

        if (count($parts) < 6) {
            throw new \InvalidArgumentException('Invalid report filename format');
        }

        return [
            'branch_code' => $parts[0],
            'office_code' => $parts[1],
            'year' => $parts[2],
            'month' => $parts[3],
            'report_type_id' => $parts[4],
            'version' => $parts[5],
        ];
    }

    /**
     * Import report from file
     *
     * @param  ReportType|null  $reportType  Optional pre-loaded report type
     */
    public function importReportFromFile(string $filePath, ?ModelsReportType $reportType = null): Report
    {
        // Check if file exists
        if (! Storage::exists($filePath)) {
            throw new \InvalidArgumentException("File not found: {$filePath}");
        }

        // Get filename from path
        $filename = basename($filePath);

        // Parse filename to get report components
        $reportComponents = $this->parseReportFilename($filename);

        // If report type not provided, try to find it
        if (! $reportType) {
            $reportType = ReportType::find($reportComponents['report_type_id']);
            if (! $reportType) {
                throw new \InvalidArgumentException("Report type not found: {$reportComponents['report_type_id']}");
            }
        }

        // Generate report code
        $reportCode = implode('.', [
            $reportComponents['branch_code'],
            $reportComponents['office_code'],
            $reportComponents['year'],
            $reportComponents['month'],
            $reportComponents['report_type_id'],
            $reportComponents['version'],
        ]);

        // Start transaction
        return DB::transaction(function () use ($filePath, $reportComponents, $reportCode, $reportType) {
            // Read file content
            $content = Storage::get($filePath);
            $lines = explode("\n", $content);

            // Parse header line
            $headerLine = $lines[0] ?? '';
            $headerParts = explode('|', $headerLine);

            // Validate header
            if (count($headerParts) < 8 || $headerParts[0] !== 'H') {
                throw new \InvalidArgumentException("Invalid header format in file: {$filePath}");
            }

            // Log header information for debugging
            Log::info("Processing file: {$filePath}");
            Log::info("Header line: {$headerLine}");
            Log::info('Header parts count: '.count($headerParts));

            // Validate that the report type in the file matches the expected report type
            $reportTypeFromFile = $reportComponents['report_type_id'];
            if ($reportType->id !== $reportTypeFromFile) {
                Log::warning("Report type mismatch: Expected {$reportType->id}, found {$reportTypeFromFile} in file {$filePath}");
            }

            $recordCount = (int) ($headerParts[6] ?? 0);

            // Create or update report
            $report = Report::updateOrCreate(
                ['report_code' => $reportCode],
                [
                    'branch_code' => $reportComponents['branch_code'],
                    'office_code' => $reportComponents['office_code'],
                    'year' => $reportComponents['year'],
                    'month' => $reportComponents['month'],
                    'report_type_id' => $reportComponents['report_type_id'],
                    'version' => $reportComponents['version'],
                    'record_count' => $recordCount,
                    'processed' => false,
                    'file_path' => $filePath,
                ]
            );

            // Delete existing data for this report
            $report->data()->delete();

            // Parse data lines
            $rowNumber = 0;

            // Get field mappings for this report type
            $fieldMappings = $this->getFieldMappings($reportComponents['report_type_id']);

            for ($i = 1; $i < count($lines); $i++) {
                $line = trim($lines[$i]);
                if (empty($line)) {
                    continue;
                }

                $parts = explode('|', $line);

                // Skip non-data lines
                if (empty($parts)) {
                    continue;
                }

                // Validate that the line has enough parts based on field mappings
                if (! empty($fieldMappings)) {
                    $maxPosition = max(array_keys($fieldMappings));
                    if (count($parts) < $maxPosition) {
                        Log::warning("Line {$i} has fewer fields (".count($parts).") than required by mapping ({$maxPosition}): {$line}");
                    }
                }

                $rowNumber++;

                // Map data to fields
                $data = [];

                // Jika kita memiliki field mappings, gunakan itu untuk mapping data
                if (! empty($fieldMappings)) {

                    // Proses semua field dalam field mappings
                    foreach ($fieldMappings as $position => $fieldName) {
                        // Posisi dalam field mapping dimulai dari 1, tapi indeks array dimulai dari 0
                        // Sesuaikan posisi untuk mengambil data dari file
                        $filePosition = $position - 1; // Kurangi 1 untuk mendapatkan indeks array yang benar

                        // Skip jika posisi di luar batas
                        if ($filePosition < 0 || $filePosition >= count($parts)) {
                            $data[$fieldName] = null;

                            continue;
                        }

                        $value = $parts[$filePosition];

                        $data[$fieldName] = $value;
                    }

                    // Pastikan semua field dalam field mapping ada dalam data
                    foreach ($fieldMappings as $position => $fieldName) {
                        if (! isset($data[$fieldName])) {
                            $data[$fieldName] = null;
                        }
                    }
                }

                // Create report data
                ReportData::create([
                    'report_id' => $report->id,
                    'row_number' => $rowNumber,
                    'data' => $data,
                ]);
            }

            // Update record count if it differs from header
            if ($rowNumber !== $recordCount) {
                $report->update(['record_count' => $rowNumber]);
            }

            return $report;
        });
    }

    /**
     * Process report data
     */
    public function processReport(Report $report): bool
    {
        // Mark report as processed
        $report->update(['processed' => true]);

        // Additional processing logic can be added here

        return true;
    }

    /**
     * Get report data as array
     *
     * @param  array  $fields  Fields to include (empty for all)
     */
    public function getReportDataAsArray(Report $report, array $fields = []): array
    {
        $query = $report->data()->orderBy('row_number');

        $data = $query->get()->map(function ($item) use ($fields) {
            if (empty($fields)) {
                return $item->data;
            }

            $filteredData = [];
            foreach ($fields as $field) {
                $filteredData[$field] = $item->getFieldValue($field);
            }

            return $filteredData;
        })->toArray();

        return $data;
    }

    public function generateReportCode($periode, string $reportTypeID): string
    {

        return implode('.', [
            config('app.branch_code'),
            config('app.office_code'),
            $periode->year,
            str_pad($periode->month, 2, '0', STR_PAD_LEFT),
            $reportTypeID,
            1,
        ]);
    }

    /**
     * Get field mappings for a report type
     */
    protected function getFieldMappings(string $reportTypeId): array
    {

        $fieldMappings = ReportType::where('id', $reportTypeId)->first();

        if ($fieldMappings && isset($fieldMappings->field_definitions['fields'])) {
            $fieldDefinitions = $fieldMappings->field_definitions['fields'];

            // Sort field definitions by position
            usort($fieldDefinitions, function ($a, $b) {
                return $a['position'] <=> $b['position'];
            });

            // Create array with index by position and value by name
            $fieldDefinitionMap = array_column($fieldDefinitions, 'name', 'position');

            return $fieldDefinitionMap;
        }

        return [];
    }
}
