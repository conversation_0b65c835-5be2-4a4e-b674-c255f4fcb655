import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { ArrowLeft, Save } from 'lucide-react';
import { Link } from '@inertiajs/react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import InputError from '@/components/input-error';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Elements',
        href: '/elements',
    },
    {
        title: 'Create Element',
        href: '/elements/create',
    },
];

type ElementForm = {
    label: string;
    field: string;
    type: string;
};

export default function ElementCreate() {
    const { data, setData, post, processing, errors } = useForm<ElementForm>({
        label: '',
        field: '',
        type: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('elements.store'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Element" />
            
            <div className="flex flex-col gap-6 p-4">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold">Create New Element</h1>
                    <Button variant="outline" asChild>
                        <Link href={route('elements.index')}>
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Elements
                        </Link>
                    </Button>
                </div>
                
                <Card>
                    <CardHeader>
                        <CardTitle>Element Information</CardTitle>
                        <CardDescription>
                            Enter the details for the new report element
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="label">Element Label</Label>
                                <Input
                                    id="label"
                                    value={data.label}
                                    onChange={(e) => setData('label', e.target.value)}
                                    placeholder="e.g., Customer Name"
                                />
                                <p className="text-sm text-muted-foreground">
                                    The human-readable name for this element.
                                </p>
                                <InputError message={errors.label} />
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="field">Field Name</Label>
                                <Input
                                    id="field"
                                    value={data.field}
                                    onChange={(e) => setData('field', e.target.value)}
                                    placeholder="e.g., customer_name"
                                />
                                <p className="text-sm text-muted-foreground">
                                    The database field or API property name. Must be unique.
                                </p>
                                <InputError message={errors.field} />
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="type">Data Type</Label>
                                <Select
                                    value={data.type}
                                    onValueChange={(value) => setData('type', value)}
                                >
                                    <SelectTrigger id="type">
                                        <SelectValue placeholder="Select a data type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="string">String</SelectItem>
                                        <SelectItem value="char">Character</SelectItem>
                                        <SelectItem value="date">Date</SelectItem>
                                        <SelectItem value="number">Number</SelectItem>
                                        <SelectItem value="boolean">Boolean</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p className="text-sm text-muted-foreground">
                                    The data type for this element.
                                </p>
                                <InputError message={errors.type} />
                            </div>
                            
                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" asChild>
                                    <Link href={route('elements.index')}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    <Save className="mr-2 h-4 w-4" />
                                    Create Element
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
