import { useState } from 'react';
import { <PERSON>, Link, router } from '@inertiajs/react';
import { toast } from 'sonner';
import { Download, FileText, RefreshCw, ChevronLeft, ChevronRight } from 'lucide-react';

import { Report } from '@/types/reports';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from '@/components/ui/pagination';

interface ShowProps {
  report: Report;
  data: {
    data: any[];
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    from: number;
    to: number;
  };
  fieldDefinitions: any[];
  filters?: {
    perPage?: string;
    page?: string;
  };
}

export default function ReportShow({ report, data, fieldDefinitions, filters = {} }: ShowProps) {
  const [activeTab, setActiveTab] = useState('table');

  const handleProcess = () => {
    router.post(route('reports.process', report.id), {}, {
      onSuccess: () => {
        toast.success("Report berhasil diproses.");
      },
      onError: (errors) => {
        toast.error(errors.process || "Gagal memproses report.");
      },
    });
  };

  const handlePerPageChange = (value: string) => {
    router.get(route('reports.show', report.id), {
      ...filters,
      perPage: value,
      page: 1
    }, {
      preserveState: true,
      replace: true,
    });
  };

  return (
    <AppLayout>
      <Head title={`Report ${report.report_code}`} />
      <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div className="p-6 bg-white border-b border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold">
              Report: {report.report_code}
            </h2>
            <div className="flex space-x-2">
              <Link href={route('reports.index')}>
                <Button variant="outline">Kembali ke Daftar</Button>
              </Link>
              {!report.processed && (
                <Button onClick={handleProcess} className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" /> Proses Report
                </Button>
              )}
            </div>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Informasi Report</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Kode Report</p>
                  <p>{report.report_code}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Tipe Report</p>
                  <Badge variant="secondary">{report.report_type_id}</Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Periode</p>
                  <p>{report.year}-{report.month}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Kode Cabang</p>
                  <p>{report.branch_code}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Kode Kantor</p>
                  <p>{report.office_code}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Versi</p>
                  <p>{report.version}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Jumlah Record</p>
                  <p>{report.record_count}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <Badge variant={report.processed ? "default" : "secondary"}>
                    {report.processed ? 'Diproses' : 'Belum Diproses'}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Tanggal Dibuat</p>
                  <p>{new Date(report.created_at).toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Report</CardTitle>
              <div className="flex justify-end space-x-2">
                <Link href={route('reports.export.json', report.id)}>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Download className="h-4 w-4" /> Export JSON
                  </Button>
                </Link>
                <Link href={route('reports.export.csv', report.id)}>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Download className="h-4 w-4" /> Export CSV
                  </Button>
                </Link>
                <Link href={report.file_path || ''} target="_blank">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" /> Lihat File Asli
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="table" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="table">Tabel</TabsTrigger>
                  <TabsTrigger value="json">JSON</TabsTrigger>
                </TabsList>
                <TabsContent value="table">
                  <div className="flex justify-between items-center mb-4">
                    <div className="text-sm text-gray-500">
                      {data.total > 0 ? (
                        <>
                          Menampilkan {data.from} - {data.to} dari {data.total} data
                        </>
                      ) : (
                        <>Tidak ada data</>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-500">Tampilkan:</span>
                      <Select
                        value={filters.perPage || '10'}
                        onValueChange={handlePerPageChange}
                      >
                        <SelectTrigger className="w-[80px]">
                          <SelectValue placeholder="10" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">5</SelectItem>
                          <SelectItem value="10">10</SelectItem>
                          <SelectItem value="25">25</SelectItem>
                          <SelectItem value="50">50</SelectItem>
                          <SelectItem value="100">100</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="rounded-md border overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {fieldDefinitions && fieldDefinitions.length > 0 ? (
                            fieldDefinitions.map((field) => (
                              <TableHead key={field.name}>{field.name}</TableHead>
                            ))
                          ) : (
                            data.data.length > 0 && Object.keys(data.data[0]).map((key) => (
                              <TableHead key={key}>{key}</TableHead>
                            ))
                          )}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data.data.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={fieldDefinitions.length || 5} className="text-center py-4 text-gray-500">
                              Tidak ada data yang ditemukan
                            </TableCell>
                          </TableRow>
                        ) : (
                          data.data.map((item, rowIndex) => (
                            <TableRow key={rowIndex}>
                              {fieldDefinitions && fieldDefinitions.length > 0 ? (
                                fieldDefinitions.map((field) => (
                                  <TableCell key={field.name}>
                                    {field.type === 'date' && item[field.name]
                                      ? new Date(item[field.name]).toLocaleDateString()
                                      : item[field.name]}
                                  </TableCell>
                                ))
                              ) : (
                                Object.keys(item).map((key) => (
                                  <TableCell key={key}>{item[key]}</TableCell>
                                ))
                              )}
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {data.last_page > 1 && (
                    <div className="mt-4">
                      <Pagination>
                        <PaginationContent>
                          {/* Tombol Previous */}
                          {data.current_page > 1 && (
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  router.get(
                                    route('reports.show', report.id),
                                    { ...filters, page: data.current_page - 1 },
                                    { preserveState: true, replace: true }
                                  );
                                }}
                              />
                            </PaginationItem>
                          )}

                          {/* Halaman pertama */}
                          {data.current_page > 3 && (
                            <PaginationItem>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  router.get(
                                    route('reports.show', report.id),
                                    { ...filters, page: 1 },
                                    { preserveState: true, replace: true }
                                  );
                                }}
                              >
                                1
                              </PaginationLink>
                            </PaginationItem>
                          )}

                          {/* Ellipsis jika halaman aktif > 4 */}
                          {data.current_page > 4 && (
                            <PaginationItem>
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}

                          {/* Halaman sebelum halaman aktif */}
                          {data.current_page > 1 && (
                            <PaginationItem>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  router.get(
                                    route('reports.show', report.id),
                                    { ...filters, page: data.current_page - 1 },
                                    { preserveState: true, replace: true }
                                  );
                                }}
                              >
                                {data.current_page - 1}
                              </PaginationLink>
                            </PaginationItem>
                          )}

                          {/* Halaman aktif */}
                          <PaginationItem>
                            <PaginationLink
                              href="#"
                              isActive={true}
                              onClick={(e) => e.preventDefault()}
                            >
                              {data.current_page}
                            </PaginationLink>
                          </PaginationItem>

                          {/* Halaman setelah halaman aktif */}
                          {data.current_page < data.last_page && (
                            <PaginationItem>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  router.get(
                                    route('reports.show', report.id),
                                    { ...filters, page: data.current_page + 1 },
                                    { preserveState: true, replace: true }
                                  );
                                }}
                              >
                                {data.current_page + 1}
                              </PaginationLink>
                            </PaginationItem>
                          )}

                          {/* Ellipsis jika halaman aktif < last_page - 3 */}
                          {data.current_page < data.last_page - 3 && (
                            <PaginationItem>
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}

                          {/* Halaman terakhir */}
                          {data.current_page < data.last_page - 2 && (
                            <PaginationItem>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  router.get(
                                    route('reports.show', report.id),
                                    { ...filters, page: data.last_page },
                                    { preserveState: true, replace: true }
                                  );
                                }}
                              >
                                {data.last_page}
                              </PaginationLink>
                            </PaginationItem>
                          )}

                          {/* Tombol Next */}
                          {data.current_page < data.last_page && (
                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  router.get(
                                    route('reports.show', report.id),
                                    { ...filters, page: data.current_page + 1 },
                                    { preserveState: true, replace: true }
                                  );
                                }}
                              />
                            </PaginationItem>
                          )}
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </TabsContent>
                <TabsContent value="json">
                  <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96 text-sm">
                    {JSON.stringify(data.data, null, 2)}
                  </pre>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
