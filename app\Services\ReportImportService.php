<?php

namespace App\Services;

use App\Models\ImportNotification;
use App\Models\ReportBatch;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ReportImportService
{
    private CsvImportService $csvService;

    public function __construct(CsvImportService $csvService)
    {
        $this->csvService = $csvService;
    }

    /**
     * Import report from CSV file
     */
    public function importFromCsv(string $filePath): ImportNotification
    {
        $startTime = microtime(true);
        $filename = basename($filePath);

        try {
            // Parse filename to get report components
            $fileInfo = $this->csvService->parseFilename($filename);
            
            // Parse CSV data
            $csvData = $this->csvService->parseCsv($filePath);
            
            // Validate CSV structure
            $this->csvService->validateCsvStructure($csvData['headers'], $fileInfo['report_code']);

            // Start database transaction
            return DB::transaction(function () use ($fileInfo, $csvData, $filename, $startTime) {
                // Find or create report batch
                $reportBatch = $this->findOrCreateReportBatch($fileInfo);

                // Delete existing data for this report batch
                $this->deleteExistingReportData($reportBatch);

                // Import new data
                $importedCount = $this->importReportData($reportBatch, $csvData['data']);

                // Update report batch
                $reportBatch->update([
                    'record_count' => $importedCount,
                    'status' => ReportBatch::STATUS_COMPLETED,
                    'processed_at' => now(),
                ]);

                // Create success notification
                $processingTime = round((microtime(true) - $startTime) * 1000, 2); // in milliseconds
                
                return ImportNotification::create([
                    'filename' => $filename,
                    'report_code' => $fileInfo['report_code'],
                    'report_month' => $fileInfo['month'],
                    'report_year' => $fileInfo['year'],
                    'records_imported' => $importedCount,
                    'status' => 'success',
                    'import_details' => [
                        'separator_detected' => $csvData['separator'],
                        'total_csv_rows' => $csvData['total_rows'],
                        'processing_time_ms' => $processingTime,
                        'report_batch_id' => $reportBatch->id,
                    ],
                ]);
            });

        } catch (Exception $e) {
            Log::error('Report import failed', [
                'filename' => $filename,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Create failure notification
            $fileInfo = $this->csvService->parseFilename($filename);
            
            return ImportNotification::create([
                'filename' => $filename,
                'report_code' => $fileInfo['report_code'] ?? 'UNKNOWN',
                'report_month' => $fileInfo['month'] ?? 0,
                'report_year' => $fileInfo['year'] ?? 0,
                'records_imported' => 0,
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'import_details' => [
                    'processing_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
                ],
            ]);
        }
    }

    /**
     * Find or create report batch for the given period
     */
    private function findOrCreateReportBatch(array $fileInfo): ReportBatch
    {
        $institutionCode = config('app.office_code', '517');
        $branchCode = config('app.branch_code', '0102');

        return ReportBatch::firstOrCreate(
            [
                'institution_code' => $institutionCode,
                'branch_code' => $branchCode,
                'report_code' => $fileInfo['report_code'],
                'report_month' => $fileInfo['month'],
                'report_year' => $fileInfo['year'],
            ],
            [
                'status' => ReportBatch::STATUS_PROCESSING,
                'record_count' => 0,
            ]
        );
    }

    /**
     * Delete existing report data for the batch
     */
    private function deleteExistingReportData(ReportBatch $reportBatch): void
    {
        $reportData = $reportBatch->getReportData();

        if ($reportData) {
            $reportData->delete();
        }
    }

    /**
     * Import report data into the appropriate table
     */
    private function importReportData(ReportBatch $reportBatch, array $data): int
    {
        $modelClass = $this->getReportModelClass($reportBatch->report_code);
        
        if (!$modelClass) {
            throw new Exception("Unsupported report type: {$reportBatch->report_code}");
        }

        $importedCount = 0;
        $batchSize = 500; // Process in batches for better performance
        $batches = array_chunk($data, $batchSize);

        foreach ($batches as $batch) {
            $insertData = [];
            
            foreach ($batch as $row) {
                $mappedData = $this->mapCsvRowToDatabase($row, $reportBatch->report_code);
                $mappedData['report_batch_id'] = $reportBatch->id;
                $mappedData['created_at'] = now();
                $mappedData['updated_at'] = now();
                
                $insertData[] = $mappedData;
            }

            if (!empty($insertData)) {
                $modelClass::insert($insertData);
                $importedCount += count($insertData);
            }
        }

        return $importedCount;
    }

    /**
     * Get the model class for a report type
     */
    private function getReportModelClass(string $reportCode): ?string
    {
        $modelMappings = [
            'A01' => \App\Models\Reports\A01::class,
            'D01' => \App\Models\Reports\D01::class,
            'D02' => \App\Models\Reports\D02::class,
            'F01' => \App\Models\Reports\F01::class,
            'F05' => \App\Models\Reports\F05::class,
            'P01' => \App\Models\Reports\P01::class,
        ];

        return $modelMappings[$reportCode] ?? null;
    }

    /**
     * Map CSV row data to database column names
     */
    private function mapCsvRowToDatabase(array $csvRow, string $reportCode): array
    {
        // This method maps CSV headers to database column names
        // The mapping should be based on the actual database schema
        
        $mappings = $this->getColumnMappings($reportCode);
        $mappedData = [];

        foreach ($mappings as $csvHeader => $dbColumn) {
            $value = $csvRow[$csvHeader] ?? null;
            
            // Clean and format the value
            if ($value !== null) {
                $value = trim($value);
                if ($value === '') {
                    $value = null;
                }
            }

            $mappedData[$dbColumn] = $value;
        }

        return $mappedData;
    }

    /**
     * Get column mappings for each report type
     */
    private function getColumnMappings(string $reportCode): array
    {
        // Map CSV headers to database column names
        $mappings = [
            'A01' => [
                'FLAG DETAIL' => 'flag_detail',
                'KODE REGISTER / NOMOR AGUNAN' => 'kode_register_nomor_agunan',
                'NOMOR REKENING FASILITAS' => 'nomor_rekening_fasilitas',
                'NOMOR CIF DEBITUR' => 'nomor_cif_debitur',
                'KODE JENIS SEGMEN FASILITAS' => 'kode_jenis_segmen_fasilitas',
                'KODE STATUS AGUNAN' => 'kode_status_agunan',
                'KODE JENIS AGUNAN' => 'kode_jenis_agunan',
                'PERINGKAT AGUNAN' => 'peringkat_agunan',
                'KODE LEMBAGA PEMERINGKAT' => 'kode_lembaga_pemeringkat',
                'KODE JENIS PENGIKATAN' => 'kode_jenis_pengikatan',
                'TANGGAL PENGIKATAN' => 'tanggal_pengikatan',
                'NAMA PEMILIK AGUNAN' => 'nama_pemilik_agunan',
                'BUKTI KEPEMILIKAN' => 'bukti_kepemilikan',
                'ALAMAT AGUNAN' => 'alamat_agunan',
                'KODE KAB/KOTA (DATI 2) LOKASI AGUNAN' => 'kode_kab_kota_lokasi_agunan',
                'NILAI AGUNAN SESUAI NJOP' => 'nilai_agunan_sesuai_njop',
                'NILAI AGUNAN MENURUT LJK' => 'nilai_agunan_menurut_ljk',
                'TANGGAL PENILAIAN LJK' => 'tanggal_penilaian_ljk',
                'NILAI AGUNAN PENILAI INDEPENDEN' => 'nilai_agunan_penilai_independen',
                'NAMA PENILAI INDEPENDEN' => 'nama_penilai_independen',
                'TANGGAL PENILAIAN PENILAI INDEPENDEN' => 'tanggal_penilaian_penilai_independen',
                'STATUS PARIPASU' => 'status_paripasu',
                'PERSENTASE PARIPASU' => 'persentase_paripasu',
                'STATUS KREDIT JOIN' => 'status_kredit_join',
                'DIASURANSIKAN' => 'diasuransikan',
                'KETERANGAN' => 'keterangan',
                'KODE KANTOR CABANG' => 'kode_kantor_cabang',
                'OPERASI DATA' => 'operasi_data',
            ],
            'D01' => [
                'FLAG DETAIL' => 'flag_detail',
                'NOMOR CIF DEBITUR' => 'nomor_cif_debitur',
                'NOMOR IDENTITAS DEBITUR' => 'nomor_identitas_debitur',
                // Add more D01 mappings based on database schema
            ],
            'D02' => [
                'FLAG DETAIL' => 'flag_detail',
                'NOMOR CIF DEBITUR' => 'nomor_cif_debitur',
                'NOMOR IDENTITAS BADAN USAHA' => 'nomor_identitas_badan_usaha',
                // Add more D02 mappings based on database schema
            ],
            'F01' => [
                'FLAG DETAIL' => 'flag_detail',
                'NOMOR REKENING FASILITAS' => 'nomor_rekening_fasilitas',
                'NOMOR CIF DEBITUR' => 'nomor_cif_debitur',
                // Add more F01 mappings based on database schema
            ],
            'F05' => [
                'FLAG DETAIL' => 'flag_detail',
                'NOMOR REKENING FASILITAS' => 'nomor_rekening_fasilitas',
                'NOMOR CIF DEBITUR' => 'nomor_cif_debitur',
                // Add more F05 mappings based on database schema
            ],
            'P01' => [
                'FLAG DETAIL' => 'flag_detail',
                'NOMOR IDENTITAS PENJAMIN' => 'nomor_identitas_penjamin',
                'NOMOR REKENING FASILITAS' => 'nomor_rekening_fasilitas',
                'NOMOR CIF DEBITUR' => 'nomor_cif_debitur',
                'KODE JENIS SEGMEN FASILITAS' => 'kode_jenis_segmen_fasilitas',
                'KODE JENIS IDENTITAS PENJAMIN' => 'kode_jenis_identitas_penjamin',
                'NAMA PENJAMIN SESUAI IDENTITAS' => 'nama_penjamin_sesuai_identitas',
                'NAMA LENGKAP PENJAMIN' => 'nama_lengkap_penjamin',
                'KODE GOLONGAN PENJAMIN' => 'kode_golongan_penjamin',
                'ALAMAT PENJAMIN' => 'alamat_penjamin',
                'PERSENTASE DIJAMIN' => 'persentase_dijamin',
                'KETERANGAN' => 'keterangan',
                'KODE KANTOR CABANG' => 'kode_kantor_cabang',
                'OPERASI DATA' => 'operasi_data',
            ],
        ];

        return $mappings[$reportCode] ?? [];
    }

    /**
     * Get all available CSV files from the seed directory
     */
    public function getAvailableCsvFiles(): array
    {
        $seedPath = 'seed';
        $files = [];

        if (!Storage::exists($seedPath)) {
            return $files;
        }

        $allFiles = Storage::files($seedPath);

        foreach ($allFiles as $file) {
            $filename = basename($file);

            // Only include CSV files that match our naming convention
            if (preg_match('/^[A-Z]\d{2}_\d{6}\.csv$/i', $filename)) {
                try {
                    $fileInfo = $this->csvService->parseFilename($filename);
                    $files[] = [
                        'filename' => $filename,
                        'path' => $file,
                        'report_code' => $fileInfo['report_code'],
                        'month' => $fileInfo['month'],
                        'year' => $fileInfo['year'],
                        'period_name' => sprintf('%02d/%d', $fileInfo['month'], $fileInfo['year']),
                        'size' => Storage::size($file),
                        'last_modified' => Storage::lastModified($file),
                    ];
                } catch (Exception $e) {
                    // Skip files that don't match our expected format
                    continue;
                }
            }
        }

        // Sort by filename
        usort($files, fn($a, $b) => strcmp($a['filename'], $b['filename']));

        return $files;
    }

    /**
     * Get import history with pagination
     */
    public function getImportHistory(array $filters = [], int $perPage = 15)
    {
        $query = ImportNotification::query()
            ->orderBy('created_at', 'desc');

        // Apply filters
        if (!empty($filters['report_code'])) {
            $query->where('report_code', $filters['report_code']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['year'])) {
            $query->where('report_year', $filters['year']);
        }

        if (!empty($filters['month'])) {
            $query->where('report_month', $filters['month']);
        }

        return $query->paginate($perPage);
    }
}
