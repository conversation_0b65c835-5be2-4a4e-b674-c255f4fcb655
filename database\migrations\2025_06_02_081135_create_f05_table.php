<?php

use App\Models\ReportBatch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_f05', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(ReportBatch::class, 'report_batch_id')->constrained()->cascadeOnDelete();


            // Basic identification fields
            $table->string('flag_detail')->nullable();
            $table->string('nomor_rekening_fasilitas')->nullable();
            $table->string('nomor_cif_debitur')->nullable();

            // Guarantee classification
            $table->string('kode_jenis_garansi')->nullable();
            $table->string('kode_tujuan_garansi')->nullable();

            // Important dates
            $table->date('tanggal_penerbitan')->nullable();
            $table->date('tanggal_jatuh_tempo')->nullable();
            $table->date('tanggal_akad_awal')->nullable();
            $table->date('tanggal_akad_akhir')->nullable();
            $table->date('tanggal_wan_prestasi')->nullable();
            $table->date('tanggal_kondisi')->nullable();

            // Contract information
            $table->string('nomor_akad_awal')->nullable();
            $table->string('nomor_akad_akhir')->nullable();
            $table->string('nama_yang_dijamin')->nullable();

            // Financial information
            $table->string('kode_valuta', 3)->nullable(); // Currency code (e.g., IDR, USD)
            $table->decimal('plafon', 20, 2)->nullable(); // Credit limit/ceiling
            $table->decimal('nominal_setoran_jaminan', 20, 2)->nullable(); // Guarantee deposit amount

            // Risk and condition codes
            $table->string('kode_kolektibilitas')->nullable(); // Collectibility code
            $table->string('kode_kondisi')->nullable(); // Condition code

            // Additional information
            $table->text('keterangan')->nullable();
            $table->string('kode_kantor_cabang')->nullable();
            $table->string('operasi_data')->nullable();

            $table->timestamps();

            // Add indexes for commonly queried fields
            $table->index('nomor_rekening_fasilitas');
            $table->index('nomor_cif_debitur');
            $table->index('kode_kantor_cabang');
            $table->index('tanggal_penerbitan');
            $table->index('tanggal_jatuh_tempo');
            $table->index('kode_jenis_garansi');
            $table->index('kode_kolektibilitas');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_f05');
    }
};