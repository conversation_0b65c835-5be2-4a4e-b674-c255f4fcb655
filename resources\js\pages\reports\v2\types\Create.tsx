import { useState } from 'react';
import { Head, <PERSON>, useForm as useInertiaForm } from '@inertiajs/react';
import { toast } from 'sonner';
import { Trash2, Plus } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';

interface CreateProps {}

export default function ReportTypeCreate() {
  const [fields, setFields] = useState<any[]>([]);
  const [fieldName, setFieldName] = useState<string>('');
  const [fieldPosition, setFieldPosition] = useState<string>('');
  const [fieldType, setFieldType] = useState<string>('');


  const { data, setData, post, processing, errors } = useInertiaForm({
    id: '',
    name: '',
    description: '',
    field_definitions: {
      fields: [],
    },
  });

  const handleChange = (name: string, value: any) => {
    setData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleAddField = () => {
    const newField = {
      name: fieldName,
      position: parseInt(fieldPosition),
      type: fieldType,
    };

    // Validate field
    if (!newField.name || !newField.position || !newField.type) {
      toast.error("Semua field harus diisi.");
      return;
    }

    // Check for duplicate position
    const existingPosition = fields.find((f) => f.position === newField.position);
    if (existingPosition) {
      toast.error("Posisi field sudah digunakan.");
      return;
    }

    // Add field
    const newFields = [...fields, newField];
    setFields(newFields);

    // Update form data
    setData((prevData) => ({
      ...prevData,
      field_definitions: {
        fields: newFields,
      },
    }));

    // Reset field form
    setFieldName('');
    setFieldPosition('');
    setFieldType('');

    toast.success(`Field ${newField.name} berhasil ditambahkan.`);
  };

  const handleRemoveField = (index: number) => {
    const fieldToRemove = fields[index];
    const newFields = [...fields];
    newFields.splice(index, 1);
    setFields(newFields);

    // Update form data
    setData((prevData) => ({
      ...prevData,
      field_definitions: {
        fields: newFields,
      },
    }));

    toast.success(`Field ${fieldToRemove.name} berhasil dihapus.`);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('report-types.store'));
  };

  return (
    <AppLayout>
      <Head title="Tambah Tipe Report Baru" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div className="p-6 bg-white border-b border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold">Tambah Tipe Report Baru</h2>
                <Link href={route('report-types.index')}>
                  <Button variant="outline">Kembali ke Daftar</Button>
                </Link>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Informasi Tipe Report</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="id">ID</Label>
                        <Input
                          id="id"
                          placeholder="Contoh: A01"
                          value={data.id}
                          onChange={(e) => handleChange('id', e.target.value)}
                          className={errors.id ? "border-destructive" : ""}
                        />
                        {errors.id && (
                          <p className="text-sm text-destructive">{errors.id}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="name">Nama</Label>
                        <Input
                          id="name"
                          placeholder="Nama tipe report"
                          value={data.name}
                          onChange={(e) => handleChange('name', e.target.value)}
                          className={errors.name ? "border-destructive" : ""}
                        />
                        {errors.name && (
                          <p className="text-sm text-destructive">{errors.name}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="description">Deskripsi</Label>
                        <textarea
                          id="description"
                          placeholder="Deskripsi tipe report"
                          value={data.description}
                          onChange={(e) => handleChange('description', e.target.value)}
                          className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${errors.description ? "border-destructive" : ""}`}
                        />
                        {errors.description && (
                          <p className="text-sm text-destructive">{errors.description}</p>
                        )}
                      </div>

                      <Card className="mt-6">
                        <CardHeader>
                          <CardTitle>Definisi Field</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div className="space-y-2">
                              <Label htmlFor="fieldName">Nama Field</Label>
                              <Input
                                id="fieldName"
                                placeholder="Contoh: account_number"
                                value={fieldName}
                                onChange={(e) => setFieldName(e.target.value)}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="fieldPosition">Posisi</Label>
                              <Input
                                id="fieldPosition"
                                type="number"
                                placeholder="Contoh: 1"
                                value={fieldPosition}
                                onChange={(e) => setFieldPosition(e.target.value)}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="fieldType">Tipe Data</Label>
                              <Select value={fieldType} onValueChange={setFieldType}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Pilih tipe data" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="string">String</SelectItem>
                                  <SelectItem value="integer">Integer</SelectItem>
                                  <SelectItem value="decimal">Decimal</SelectItem>
                                  <SelectItem value="date">Date</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div className="flex justify-end">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={handleAddField}
                              className="flex items-center gap-2"
                            >
                              <Plus className="h-4 w-4" /> Tambah Field
                            </Button>
                          </div>

                          {fields.length > 0 && (
                            <div className="mt-4 border rounded-md">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>Nama Field</TableHead>
                                    <TableHead>Posisi</TableHead>
                                    <TableHead>Tipe Data</TableHead>
                                    <TableHead className="w-[100px]">Aksi</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {fields.map((field, index) => (
                                    <TableRow key={`${field.name}-${field.position}`}>
                                      <TableCell>{field.name}</TableCell>
                                      <TableCell>{field.position}</TableCell>
                                      <TableCell>{field.type}</TableCell>
                                      <TableCell>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleRemoveField(index)}
                                          className="h-8 w-8 p-0 text-destructive"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>

                    <div className="flex justify-end mt-6">
                      <Button
                        type="submit"
                        disabled={processing}
                      >
                        {processing ? "Menyimpan..." : "Simpan"}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
