import { Head, <PERSON>, router } from '@inertiajs/react';
import { FileText, Filter, Download, ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';

import { Report, ReportType } from '@/types/reports';
import AppLayout from '@/layouts/app-layout';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from '@/components/ui/pagination';
import { useForm as useReactHookForm } from 'react-hook-form';

interface IndexProps {
  reports: {
    data: Report[];
    links: any;
    current_page: number
    first_page_url: string
    from: number
    last_page: number
    last_page_url: string
    next_page_url: string
    path: string
    per_page: number
    prev_page_url: any
    to: number
    total: number

  };
  reportTypes: ReportType[];
  filters: {
    report_type_id?: string;
    year?: string;
    month?: string;
    perPage?: string;
  };
}

export default function ReportsIndex({ reports, reportTypes, filters }: IndexProps) {
  const form = useReactHookForm({
    defaultValues: {
      report_type_id: filters.report_type_id || '',
      year: filters.year || '',
      month: filters.month || '',
      perPage: filters.perPage || '10',
    }
  });

  const handleFilterChange = (values: any) => {
    router.get(route('reports.index'), values, {
      preserveState: true,
      replace: true,
    });
  };

  const handleResetFilter = () => {
    // Reset form values
    form.reset({
      report_type_id: '',
      year: '',
      month: '',
      perPage: '10',
    });

    // Navigate to the page with reset filters
    router.get(route('reports.index'), {
      perPage: '10',
      page: 1
    }, {
      preserveState: true,
      replace: true,
    });
  };

  const handlePerPageChange = (value: string) => {
    router.get(route('reports.index'), { ...filters, perPage: value, page: 1 }, {
      preserveState: true,
      replace: true,
    });
  };

  return (
    <AppLayout>
      <Head title="Hybrid Reports" />

      <div className="sm:px-6 lg:px-8">
        <div className="p-6 bg-white border-b border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold">Reports</h2>
            <Link href={route('reports.create')}>
              <Button variant="default">Tambah Report Baru</Button>
            </Link>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Filter</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleFilterChange)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="report_type_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tipe Report</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Pilih tipe report" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {reportTypes.map((type) => (
                                <SelectItem key={type.id} value={type.id}>
                                  {type.id} - {type.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="year"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tahun</FormLabel>
                          <FormControl>
                            <Input placeholder="Contoh: 2025" {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="month"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bulan</FormLabel>
                          <FormControl>
                            <Input placeholder="Contoh: 03" {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      className="flex items-center gap-1"
                      onClick={handleResetFilter}
                    >
                      <RefreshCw className="h-4 w-4" />
                      Reset
                    </Button>
                    <Button type="submit" className="flex items-center gap-1">
                      <Filter className="h-4 w-4" />
                      Filter
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>

          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-gray-500">
              {reports.total > 0 ? (
                <>
                  Menampilkan {reports.from} - {reports.to} dari {reports.total} data
                </>
              ) : (
                <>Tidak ada data</>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Tampilkan:</span>
              <Select
                value={filters.perPage || '10'}
                onValueChange={handlePerPageChange}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder="10" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Kode Report</TableHead>
                  <TableHead>Tipe Report</TableHead>
                  <TableHead>Periode</TableHead>
                  <TableHead>Jumlah Record</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Tanggal Dibuat</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reports.data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4 text-gray-500">
                      Tidak ada data yang ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  reports.data.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>
                        <Link href={route('reports.show', report.id)} className="text-blue-600 hover:underline">
                          {report.report_code}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{report.report_type_id}</Badge>
                      </TableCell>
                      <TableCell>{report.year}-{report.month}</TableCell>
                      <TableCell>{report.record_count}</TableCell>
                      <TableCell>
                        <Badge variant={report.processed ? "default" : "secondary"} className={report.processed ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}>
                          {report.processed ? 'Diproses' : 'Belum Diproses'}
                        </Badge>
                      </TableCell>
                      <TableCell>{new Date(report.created_at).toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Link href={route('reports.show', report.id)}>
                            <Button variant="default" size="sm" className="flex items-center gap-1">
                              <FileText className="h-4 w-4" />
                              Detail
                            </Button>
                          </Link>
                          <Link href={route('reports.export.json', report.id)}>
                            <Button variant="outline" size="sm" className="flex items-center gap-1">
                              <Download className="h-4 w-4" />
                              JSON
                            </Button>
                          </Link>
                          <Link href={route('reports.export.csv', report.id)}>
                            <Button variant="outline" size="sm" className="flex items-center gap-1">
                              <Download className="h-4 w-4" />
                              CSV
                            </Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {reports && reports.last_page > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  {/* Tombol Previous */}
                  {reports.current_page > 1 && (
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          router.get(
                            route('reports.index'),
                            { ...filters, page: reports.current_page - 1 },
                            { preserveState: true, replace: true }
                          );
                        }}
                      />
                    </PaginationItem>
                  )}

                  {/* Halaman pertama */}
                  {reports.current_page > 3 && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          router.get(
                            route('reports.index'),
                            { ...filters, page: 1 },
                            { preserveState: true, replace: true }
                          );
                        }}
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis jika halaman aktif > 4 */}
                  {reports.current_page > 4 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Halaman sebelum halaman aktif */}
                  {reports.current_page > 1 && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          router.get(
                            route('reports.index'),
                            { ...filters, page: reports.current_page - 1 },
                            { preserveState: true, replace: true }
                          );
                        }}
                      >
                        {reports.current_page - 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Halaman aktif */}
                  <PaginationItem>
                    <PaginationLink
                      href="#"
                      isActive={true}
                      onClick={(e) => e.preventDefault()}
                    >
                      {reports.current_page}
                    </PaginationLink>
                  </PaginationItem>

                  {/* Halaman setelah halaman aktif */}
                  {reports.current_page < reports.last_page && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          router.get(
                            route('reports.index'),
                            { ...filters, page: reports.current_page + 1 },
                            { preserveState: true, replace: true }
                          );
                        }}
                      >
                        {reports.current_page + 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis jika halaman aktif < last_page - 3 */}
                  {reports.current_page < reports.last_page - 3 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Halaman terakhir */}
                  {reports.current_page < reports.last_page - 2 && (
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          router.get(
                            route('reports.index'),
                            { ...filters, page: reports.last_page },
                            { preserveState: true, replace: true }
                          );
                        }}
                      >
                        {reports.last_page}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Tombol Next */}
                  {reports.current_page < reports.last_page && (
                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          router.get(
                            route('reports.index'),
                            { ...filters, page: reports.current_page + 1 },
                            { preserveState: true, replace: true }
                          );
                        }}
                      />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}
