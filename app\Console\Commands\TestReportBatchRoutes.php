<?php

namespace App\Console\Commands;

use App\Models\ReportBatch;
use App\Models\User;
use App\Services\ReportBatchService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;

class TestReportBatchRoutes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-report-routes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Report Batch routes and functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Report Batch Routes and Functionality...');

        try {
            // Test 1: Check if routes are registered
            $this->info("\n1️⃣ Testing route registration");
            $routes = collect(Route::getRoutes())->filter(function ($route) {
                return str_contains($route->getName() ?? '', 'report');
            });

            $this->info("✅ Found {$routes->count()} report routes:");
            foreach ($routes as $route) {
                $this->line("   - {$route->methods()[0]} {$route->uri()} ({$route->getName()})");
            }

            // Test 2: Test service functionality
            $this->info("\n2️⃣ Testing ReportBatchService");
            $service = new ReportBatchService();
            
            // Test dashboard statistics
            $stats = $service->getDashboardStatistics(2024);
            $this->info("✅ Dashboard stats: {$stats['total_batches']} total, {$stats['completed_batches']} completed");

            // Test current period suggestion
            $period = $service->getCurrentPeriodSuggestion();
            $this->info("✅ Current period suggestion: {$period['period_name']}");

            // Test 3: Test model functionality
            $this->info("\n3️⃣ Testing ReportBatch model");
            
            // Create a test batch
            $batch = ReportBatch::create([
                'institution_code' => 'TEST001',
                'branch_code' => 'BR001',
                'report_code' => 'A01',
                'report_month' => 1,
                'report_year' => 2023,
                'status' => 'pending'
            ]);

            $this->info("✅ Created test batch: {$batch->period_name}");
            $this->info("   - Status: {$batch->status_label}");
            $this->info("   - Completion: {$batch->completion_percentage}%");

            // Test period calculations
            $previous = $batch->getPreviousPeriod();
            $comparison = $batch->getComparisonPeriod();
            $this->info("   - Previous period: {$previous['year']}-{$previous['month']}");
            $this->info("   - Comparison period: {$comparison['year']}-{$comparison['month']}");

            // Test status methods
            $this->info("   - Is pending: " . ($batch->isProcessing() ? 'Yes' : 'No'));
            $this->info("   - Is completed: " . ($batch->isCompleted() ? 'Yes' : 'No'));
            $this->info("   - Has failed: " . ($batch->hasFailed() ? 'Yes' : 'No'));

            // Test 4: Test validation
            $this->info("\n4️⃣ Testing data validation");
            $validation = $service->validateDataAvailability($batch);
            $this->info("✅ Validation completed:");
            $this->info("   - Valid: " . ($validation['valid'] ? 'Yes' : 'No'));
            $this->info("   - Warnings: " . count($validation['warnings']));
            $this->info("   - Errors: " . count($validation['errors']));

            // Test 5: Test status transitions
            $this->info("\n5️⃣ Testing status transitions");
            
            $batch->markAsProcessing();
            $this->info("✅ Marked as processing: {$batch->fresh()->status}");
            
            $batch->markAsCompleted(1500);
            $this->info("✅ Marked as completed: {$batch->fresh()->status} with {$batch->fresh()->record_count} records");

            // Test 6: Test scopes and queries
            $this->info("\n6️⃣ Testing model scopes");
            
            $completedCount = ReportBatch::completed()->count();
            $this->info("✅ Completed batches: {$completedCount}");
            
            $pendingCount = ReportBatch::withStatus('pending')->count();
            $this->info("✅ Pending batches: {$pendingCount}");
            
            $periodCount = ReportBatch::forPeriod(2024, 1)->count();
            $this->info("✅ Batches for Jan 2024: {$periodCount}");

            // Test 7: Test pagination
            $this->info("\n7️⃣ Testing pagination");
            $paginated = $service->getPaginatedReportBatches(['year' => 2024], 10);
            $this->info("✅ Paginated results: {$paginated->count()} of {$paginated->total()} total");

            // Test 8: Test report data relationships
            $this->info("\n8️⃣ Testing report data relationships");
            $reportData = $batch->getReportData();
            $this->info("✅ Report data relationship: " . get_class($reportData));

            // Clean up
            $batch->delete();
            $this->info("🧹 Cleaned up test data");

            // Test 9: Test configuration
            $this->info("\n9️⃣ Testing configuration");
            $reportCodes = array_keys(config('report-mapping', []));
            $this->info("✅ Available report codes: " . implode(', ', $reportCodes));

            $institutionCode = config('app.office_code', 'Not set');
            $branchCode = config('app.branch_code', 'Not set');
            $this->info("✅ Institution code: {$institutionCode}");
            $this->info("✅ Branch code: {$branchCode}");

            // Test 10: Test summary statistics
            $this->info("\n🔟 Testing summary statistics");
            $allBatches = ReportBatch::all();
            $statusBreakdown = $allBatches->groupBy('status')->map->count();
            
            $this->info("✅ Status breakdown:");
            foreach ($statusBreakdown as $status => $count) {
                $this->info("   - {$status}: {$count}");
            }

            $reportBreakdown = $allBatches->groupBy('report_code')->map->count();
            $this->info("✅ Report code breakdown:");
            foreach ($reportBreakdown as $code => $count) {
                $this->info("   - {$code}: {$count}");
            }

            $this->info("\n🎉 All route and functionality tests passed!");

        } catch (\Exception $e) {
            $this->error("❌ Error during testing: " . $e->getMessage());
            $this->error("📍 File: " . $e->getFile() . ":" . $e->getLine());
            return 1;
        }

        return 0;
    }
}
