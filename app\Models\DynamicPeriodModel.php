<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Env;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

abstract class DynamicPeriodModel extends Model
{
    public $timestamps = false;

    protected string $defaultConnection = 'dwh_dynamic';

    /**
     * Set koneksi database dan nama tabel berdasarkan periode.
     *
     * @param  string|null  $period  Format: YYYYMM
     */
    public function setPeriod(?string $period = null): static
    {
        $period = $period ?? now()->format('Ym');

        $year = substr($period, 0, 4);
        $month = substr($period, 4, 2);

        // Set nama database dan nama tabel
        $database_prefix = Env::getOrFail('DB_NAME_PREFIX_DWH');

        // Try different database formats
        $database = "{$database_prefix}_{$year}";
        $table = static::tablePrefix()."_{$year}{$month}";

        Config::set("database.connections.{$this->defaultConnection}.database", $database);

        // DB::purge($this->defaultConnection);

        $this->setConnection($this->defaultConnection);
        $this->setTable($table);

        return $this;
    }

    /**
     * Harus diimplementasi di model turunan: prefix nama tabel.
     */
    abstract protected static function tablePrefix(): string;
}
