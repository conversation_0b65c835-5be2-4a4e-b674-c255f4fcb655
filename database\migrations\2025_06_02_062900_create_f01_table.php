<?php

use App\Models\ReportBatch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_f01', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(ReportBatch::class, 'report_batch_id')->constrained()->cascadeOnDelete();

            $table->string('flag_detail')->nullable();
            $table->string('nomor_rekening_fasilitas')->nullable();
            $table->string('nomor_cif_debitur')->nullable();
            $table->string('kode_sifat_kredit')->nullable();
            $table->string('kode_jenis_kredit')->nullable();
            $table->string('kode_skim_akad_pembiayaan')->nullable();
            $table->string('nomor_akad_awal')->nullable();
            $table->date('tanggal_akad_awal')->nullable();
            $table->string('nomor_akad_akhir')->nullable();
            $table->date('tanggal_akad_akhir')->nullable();
            $table->string('baru_atau_perpanjangan')->nullable();
            $table->date('tanggal_awal_kredit')->nullable();
            $table->date('tanggal_mulai')->nullable();
            $table->date('tanggal_jatuh_tempo')->nullable();
            $table->string('kode_kategori_debitur')->nullable();
            $table->string('kode_jenis_penggunaan')->nullable();
            $table->string('kode_orientasi_penggunaan')->nullable();
            $table->string('kode_sektor_ekonomi')->nullable();
            $table->string('kode_kab_kota_lokasi_proyek')->nullable();
            $table->decimal('nilai_proyek', 20, 2)->nullable();
            $table->string('kode_valuta')->nullable();
            $table->decimal('persentase_suku_bunga', 5, 2)->nullable();
            $table->string('jenis_suku_bunga')->nullable();
            $table->string('kredit_program_pemerintah')->nullable();
            $table->string('takeover_dari')->nullable();
            $table->string('sumber_dana')->nullable();
            $table->decimal('plafon_awal', 20, 2)->nullable();
            $table->decimal('plafon', 20, 2)->nullable();
            $table->decimal('realisasi_bulan_berjalan', 20, 2)->nullable();
            $table->decimal('denda', 20, 2)->nullable();
            $table->decimal('baki_debet', 20, 2)->nullable();
            $table->decimal('nilai_dalam_mata_uang_asal', 20, 2)->nullable();
            $table->string('kode_kolektibilitas')->nullable();
            $table->date('tanggal_macet')->nullable();
            $table->string('kode_sebab_macet')->nullable();
            $table->decimal('tunggakan_pokok', 20, 2)->nullable();
            $table->decimal('tunggakan_bunga', 20, 2)->nullable();
            $table->integer('jumlah_hari_tunggakan')->nullable();
            $table->integer('frekuensi_tunggakan')->nullable();
            $table->integer('frekuensi_restrukturisasi')->nullable();
            $table->date('tanggal_restrukturisasi_awal')->nullable();
            $table->date('tanggal_restrukturisasi_akhir')->nullable();
            $table->string('kode_cara_restrukturisasi')->nullable();
            $table->string('kode_kondisi')->nullable();
            $table->date('tanggal_kondisi')->nullable();
            $table->text('keterangan')->nullable();
            $table->string('kode_kantor_cabang')->nullable();
            $table->string('operasi_data')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_f01');
    }
};
