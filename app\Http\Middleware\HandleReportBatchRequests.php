<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class HandleReportBatchRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only handle JSON responses for report batch routes
        if ($request->is('report*') && $request->expectsJson()) {
            return $this->handleJsonResponse($response, $request);
        }

        return $response;
    }

    /**
     * Handle JSON responses for report batch requests
     */
    private function handleJsonResponse(Response $response, Request $request): JsonResponse
    {
        $statusCode = $response->getStatusCode();
        $content = $response->getContent();

        // If it's already a proper JSON response, return as is
        if ($response instanceof JsonResponse) {
            return $response;
        }

        // Handle validation errors
        if ($statusCode === 422) {
            try {
                $errors = json_decode($content, true);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $errors['errors'] ?? $errors,
                ], 422);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => ['general' => ['Invalid request data']],
                ], 422);
            }
        }

        // Handle server errors
        if ($statusCode >= 500) {
            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error' => config('app.debug') ? $content : 'An unexpected error occurred',
            ], $statusCode);
        }

        // Handle client errors
        if ($statusCode >= 400) {
            return response()->json([
                'success' => false,
                'message' => $this->getErrorMessage($statusCode),
                'error' => $content,
            ], $statusCode);
        }

        // Handle successful responses
        if ($statusCode >= 200 && $statusCode < 300) {
            try {
                $data = json_decode($content, true);
                return response()->json([
                    'success' => true,
                    'data' => $data,
                ], $statusCode);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => true,
                    'message' => 'Operation completed successfully',
                ], $statusCode);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'Unknown response',
        ], $statusCode);
    }

    /**
     * Get error message based on status code
     */
    private function getErrorMessage(int $statusCode): string
    {
        return match($statusCode) {
            400 => 'Bad request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Resource not found',
            405 => 'Method not allowed',
            409 => 'Conflict',
            422 => 'Validation failed',
            429 => 'Too many requests',
            default => 'Client error',
        };
    }
}
