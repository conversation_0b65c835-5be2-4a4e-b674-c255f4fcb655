<?php

namespace App\Http\Requests\ReportBatch;

use App\Models\ReportBatch;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateReportBatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $reportBatch = $this->route('reportBatch');
        
        return [
            'status' => [
                'sometimes',
                'string',
                Rule::in([
                    ReportBatch::STATUS_PENDING,
                    ReportBatch::STATUS_PROCESSING,
                    ReportBatch::STATUS_COMPLETED,
                    ReportBatch::STATUS_FAILED,
                ]),
            ],
            'record_count' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'validation_results' => [
                'sometimes',
                'array',
            ],
            'error_message' => [
                'sometimes',
                'string',
                'nullable',
            ],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'status.in' => 'Invalid status. Must be one of: pending, processing, completed, failed.',
            'record_count.integer' => 'Record count must be an integer.',
            'record_count.min' => 'Record count must be at least 0.',
            'validation_results.array' => 'Validation results must be an array.',
            'error_message.string' => 'Error message must be a string.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $reportBatch = $this->route('reportBatch');
            
            // Prevent updating completed reports unless explicitly allowed
            if ($reportBatch && $reportBatch->isCompleted() && $this->has('status')) {
                if ($this->status !== ReportBatch::STATUS_COMPLETED) {
                    $validator->errors()->add(
                        'status',
                        'Cannot change status of a completed report batch.'
                    );
                }
            }

            // Validate status transitions
            if ($reportBatch && $this->has('status')) {
                $currentStatus = $reportBatch->status;
                $newStatus = $this->status;

                $validTransitions = [
                    ReportBatch::STATUS_PENDING => [
                        ReportBatch::STATUS_PROCESSING,
                        ReportBatch::STATUS_FAILED,
                    ],
                    ReportBatch::STATUS_PROCESSING => [
                        ReportBatch::STATUS_COMPLETED,
                        ReportBatch::STATUS_FAILED,
                        ReportBatch::STATUS_PENDING, // Allow reset
                    ],
                    ReportBatch::STATUS_FAILED => [
                        ReportBatch::STATUS_PENDING,
                        ReportBatch::STATUS_PROCESSING,
                    ],
                    ReportBatch::STATUS_COMPLETED => [
                        // Generally no transitions allowed from completed
                    ],
                ];

                if (!in_array($newStatus, $validTransitions[$currentStatus] ?? [])) {
                    $validator->errors()->add(
                        'status',
                        "Invalid status transition from {$currentStatus} to {$newStatus}."
                    );
                }
            }
        });
    }
}
