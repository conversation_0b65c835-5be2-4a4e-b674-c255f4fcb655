curl.cainfo=/home/<USER>/.config/herd-lite/bin/cacert.pem
openssl.cafile=/home/<USER>/.config/herd-lite/bin/cacert.pem
pcre.jit=0

; Microsoft SQL Server Configuration
; SQL Server extensions - Currently commented out due to version incompatibility
; The extensions were compiled for PHP 8.1 but Herd Lite uses PHP 8.4.1
;extension=sqlsrv.so
;extension=pdo_sqlsrv.so

; SQL Server Connection Settings
mssql.secure_connection = On
mssql.charset = "UTF-8"
mssql.timeout = 60
mssql.max_persistent = -1
mssql.max_links = -1
