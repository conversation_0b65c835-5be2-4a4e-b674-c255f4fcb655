<?php

use App\Models\ReportBatch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_a01', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(ReportBatch::class, 'report_batch_id')->constrained()->cascadeOnDelete();

            // Basic identification fields
            $table->string('flag_detail')->nullable();
            $table->string('kode_register_nomor_agunan')->nullable();
            $table->string('nomor_rekening_fasilitas')->nullable();
            $table->string('nomor_cif_debitur')->nullable();

            // Classification codes
            $table->string('kode_jenis_segmen_fasilitas')->nullable();
            $table->string('kode_status_agunan')->nullable();
            $table->string('kode_jenis_agunan')->nullable();
            $table->string('peringkat_agunan')->nullable();
            $table->string('kode_lembaga_pemeringkat')->nullable();
            $table->string('kode_jenis_pengikatan')->nullable();

            // Dates
            $table->date('tanggal_pengikatan')->nullable();
            $table->date('tanggal_penilaian_ljk')->nullable();
            $table->date('tanggal_penilaian_penilai_independen')->nullable();

            // Owner and property information
            $table->string('nama_pemilik_agunan')->nullable();
            $table->string('bukti_kepemilikan')->nullable();
            $table->text('alamat_agunan')->nullable();
            $table->string('kode_kab_kota_lokasi_agunan')->nullable();

            // Valuation amounts (using decimal for monetary values)
            $table->decimal('nilai_agunan_sesuai_njop', 20, 2)->nullable();
            $table->decimal('nilai_agunan_menurut_ljk', 20, 2)->nullable();
            $table->decimal('nilai_agunan_penilai_independen', 20, 2)->nullable();

            // Appraiser information
            $table->string('nama_penilai_independen')->nullable();

            // Status and percentage fields
            $table->string('status_paripasu')->nullable();
            $table->decimal('persentase_paripasu', 5, 2)->nullable(); // Assuming percentage with 2 decimal places
            $table->string('status_kredit_join')->nullable();
            $table->string('diasuransikan')->nullable();

            // Additional information
            $table->text('keterangan')->nullable();
            $table->string('kode_kantor_cabang')->nullable();
            $table->string('operasi_data')->nullable();

            $table->timestamps();

            // Add indexes for commonly queried fields
            $table->index('kode_register_nomor_agunan');
            $table->index('nomor_cif_debitur');
            $table->index('kode_kantor_cabang');
            $table->index('tanggal_pengikatan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_a01');
    }
};