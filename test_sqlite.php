<?php

// Test SQLite connection
try {
    $db = new PDO('sqlite::memory:');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create a test table
    $db->exec('CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)');

    // Insert some data
    $db->exec("INSERT INTO test (name) VALUES ('SQLite Test')");

    // Query the data
    $stmt = $db->query('SELECT * FROM test');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "SQLite connection successful!\n";
    echo 'Retrieved data: ID = '.$result['id'].', Name = '.$result['name']."\n";
} catch (PDOException $e) {
    echo 'SQLite connection failed: '.$e->getMessage()."\n";
}
