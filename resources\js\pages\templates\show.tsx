import { Head } from '@inertiajs/react';
import { <PERSON> } from '@inertiajs/react';
import { ArrowLeft, FileText, Pencil, PuzzleIcon, Trash2 } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

interface ReportElement {
    id: number;
    label: string;
    field: string;
    type: string;
}

interface TemplateDetail {
    id: number;
    report_template_id: number;
    report_element_id: number;
    order: number;
    element: ReportElement;
}

interface Template {
    id: number;
    name: string;
    alias: string;
    description: string;
    details: TemplateDetail[];
}

interface TemplateShowProps {
    template: Template;
}

export default function TemplateShow({ template }: TemplateShowProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Templates',
            href: '/templates',
        },
        {
            title: template.name,
            href: route('templates.show', { template: template.id }),
        },
    ];
    
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Template - ${template.name}`} />
            
            <div className="flex flex-col gap-6 p-4">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">{template.name}</h1>
                        <p className="text-muted-foreground">Template Code: {template.alias}</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href={route('templates.index')}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Templates
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={route('templates.edit', { template: template.id })}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit Template
                            </Link>
                        </Button>
                        <Button asChild>
                            <Link href={route('templates.builder', { template: template.id })}>
                                <PuzzleIcon className="mr-2 h-4 w-4" />
                                Template Builder
                            </Link>
                        </Button>
                    </div>
                </div>
                
                <Card>
                    <CardHeader>
                        <CardTitle>Template Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                                <p className="mt-1">{template.description}</p>
                            </div>
                            
                            <Separator />
                            
                            <div>
                                <div className="flex justify-between items-center mb-2">
                                    <h3 className="text-sm font-medium text-muted-foreground">Elements ({template.details.length})</h3>
                                    <Button variant="outline" size="sm" asChild>
                                        <Link href={route('templates.builder', { template: template.id })}>
                                            Manage Elements
                                        </Link>
                                    </Button>
                                </div>
                                
                                {template.details.length === 0 ? (
                                    <div className="text-center py-8 border border-dashed rounded-md">
                                        <p className="text-muted-foreground">No elements added to this template yet.</p>
                                        <Button variant="outline" size="sm" className="mt-2" asChild>
                                            <Link href={route('templates.builder', { template: template.id })}>
                                                Add Elements
                                            </Link>
                                        </Button>
                                    </div>
                                ) : (
                                    <div className="border rounded-md overflow-hidden">
                                        <table className="w-full">
                                            <thead>
                                                <tr className="bg-muted/50">
                                                    <th className="px-4 py-2 text-left text-sm font-medium">Order</th>
                                                    <th className="px-4 py-2 text-left text-sm font-medium">Label</th>
                                                    <th className="px-4 py-2 text-left text-sm font-medium">Field</th>
                                                    <th className="px-4 py-2 text-left text-sm font-medium">Type</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {template.details
                                                    .sort((a, b) => a.order - b.order)
                                                    .map((detail) => (
                                                        <tr key={detail.id} className="border-t">
                                                            <td className="px-4 py-2 text-sm">{detail.order}</td>
                                                            <td className="px-4 py-2 text-sm font-medium">{detail.element.label}</td>
                                                            <td className="px-4 py-2 text-sm font-mono text-xs">{detail.element.field}</td>
                                                            <td className="px-4 py-2">
                                                                <Badge variant="outline">{detail.element.type}</Badge>
                                                            </td>
                                                        </tr>
                                                    ))
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <div className="flex justify-between items-center">
                    <Button variant="outline" asChild>
                        <Link href={route('templates.index')}>
                            Back to Templates
                        </Link>
                    </Button>
                    
                    <Button variant="destructive" asChild>
                        <Link 
                            href={route('templates.destroy', { template: template.id })} 
                            method="delete" 
                            as="button"
                        >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Template
                        </Link>
                    </Button>
                </div>
            </div>
        </AppLayout>
    );
}
