import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Pencil } from 'lucide-react';

import { ReportType } from '@/types/reports';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';

interface ShowProps {
  reportType: ReportType;
}

export default function ReportTypeShow({ reportType }: ShowProps) {
  // Definisi field akan ditampilkan menggunakan Shadcn UI Table

  const fields = reportType.field_definitions?.fields || [];

  return (
    <AppLayout>
      <Head title={`Tipe Report ${reportType.id}`} />
      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6 bg-white border-b border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold">
              Tipe Report: {reportType.id}
            </h2>
            <div className="space-x-2">
              <Link href={route('report-types.index')}>
                <Button variant="outline">Kembali ke Daftar</Button>
              </Link>
              <Link href={route('report-types.edit', reportType.id)}>
                <Button variant="default" className="flex items-center gap-1">
                  <Pencil className="h-4 w-4" /> Edit
                </Button>
              </Link>
            </div>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Informasi Tipe Report</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">ID</p>
                  <Badge variant="secondary">{reportType.id}</Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Nama</p>
                  <p>{reportType.name}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Jumlah Field</p>
                  <p>{fields.length}</p>
                </div>
                <div className="space-y-1 col-span-3">
                  <p className="text-sm font-medium text-muted-foreground">Deskripsi</p>
                  <p>{reportType.description || '-'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Tanggal Dibuat</p>
                  <p>{new Date(reportType.created_at).toLocaleString()}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Terakhir Diperbarui</p>
                  <p>{new Date(reportType.updated_at).toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Definisi Field</CardTitle>
            </CardHeader>
            <CardContent>
              {fields.length > 0 ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nama Field</TableHead>
                        <TableHead>Posisi</TableHead>
                        <TableHead>Tipe Data</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {fields.map((field) => (
                        <TableRow key={`${field.name}-${field.position}`}>
                          <TableCell>{field.name}</TableCell>
                          <TableCell>{field.position}</TableCell>
                          <TableCell>
                            <Badge variant="secondary">{field.type}</Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p>Tidak ada definisi field.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}