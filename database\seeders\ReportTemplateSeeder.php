<?php

namespace Database\Seeders;

use App\Models\ReportTemplate;
use Illuminate\Database\Seeder;

class ReportTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing templates to avoid duplicates
        ReportTemplate::truncate();

        // Create templates based on DOCS.md list
        $templates = [
            [
                'name' => 'Debitur Perseorangan',
                'alias' => 'D01',
                'description' => 'Laporan data debitur perorangan termasuk informasi identitas dan data pribadi sesuai format SLIK',
            ],
            [
                'name' => 'Debitur Badan Usaha',
                'alias' => 'D02',
                'description' => 'Laporan data debitur badan usaha termasuk informasi identitas dan data perusahaan sesuai format SLIK',
            ],
            [
                'name' => 'Fasilitas Kredit atau Pembiayaan',
                'alias' => 'F01',
                'description' => 'Laporan fasilitas kredit atau pembiayaan yang diberikan kepada debitur sesuai format SLIK',
            ],
            [
                'name' => 'Fasilitas Surat Berharga',
                'alias' => 'F03',
                'description' => 'Laporan fasilitas surat berharga yang dimiliki oleh debitur sesuai format SLIK',
            ],
            [
                'name' => 'Fasilitas Garansi yang Diberikan',
                'alias' => 'F05',
                'description' => 'Laporan fasilitas garansi yang diberikan kepada debitur sesuai format SLIK',
            ],
            [
                'name' => 'Agunan',
                'alias' => 'P01',
                'description' => 'Laporan data agunan yang dijaminkan oleh debitur sesuai format SLIK',
            ],
        ];

        foreach ($templates as $template) {
            ReportTemplate::create($template);
        }
    }
}
