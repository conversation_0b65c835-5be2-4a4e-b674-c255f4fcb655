import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, <PERSON>cil, Trash2 } from "lucide-react";
import { Link, router } from "@inertiajs/react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table";
import { toast } from "sonner";

export type Element = {
  id: number;
  label: string;
  field: string;
  type: string;
  template_details_count: number;
  created_at: string;
  updated_at: string;
};

// Element types for filtering
export const elementTypes = [
  { value: "string", label: "String" },
  { value: "char", label: "Character" },
  { value: "date", label: "Date" },
  { value: "number", label: "Number" },
  { value: "boolean", label: "Boolean" },
];

export const columns: ColumnDef<Element>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row }) => <div className="font-medium">{row.getValue("id")}</div>,
    enableHiding: false,
  },
  {
    accessorKey: "label",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Label" />
    ),
    cell: ({ row }) => <div>{row.getValue("label")}</div>,
  },
  {
    accessorKey: "field",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Field" />
    ),
    cell: ({ row }) => (
      <div className="font-mono text-sm">{row.getValue("field")}</div>
    ),
  },
  {
    accessorKey: "type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Type" />
    ),
    cell: ({ row }) => (
      <Badge variant="outline">{row.getValue("type")}</Badge>
    ),
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "template_details_count",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Used In" />
    ),
    cell: ({ row }) => {
      const count = parseInt(row.getValue("template_details_count"));
      return (
        <div className="text-center">
          {count > 0 ? (
            <Badge variant="secondary">
              {count} template{count !== 1 ? "s" : ""}
            </Badge>
          ) : (
            <span className="text-muted-foreground text-sm">Not used</span>
          )}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const element = row.original;

      const handleDelete = () => {
        if (element.template_details_count > 0) {
          toast.error("Cannot delete element as it is used in templates");
          return;
        }

        if (confirm("Are you sure you want to delete this element?")) {
          router.delete(route("elements.destroy", { element: element.id }), {
            onSuccess: () => {
              toast.success("Element deleted successfully");
            },
            onError: () => {
              toast.error("Failed to delete element");
            },
          });
        }
      };

      return (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={route("elements.show", { element: element.id })}>
                  View details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={route("elements.edit", { element: element.id })}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive focus:text-destructive"
                disabled={element.template_details_count > 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];
