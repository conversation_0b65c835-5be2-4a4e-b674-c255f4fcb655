<?php

namespace App\Console\Commands;

use App\Models\ReportBatch;
use Illuminate\Console\Command;

class GenerateReportBatch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-report-batch {year : Year to generate reports for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $year = $this->argument('year');

        // Validate year: must be a 4-digit integer between 1900 and current year
        if (!preg_match('/^\d{4}$/', $year) || (int) $year < 1900 || (int) $year > (int) date('Y')) {
            $this->error('Invalid year provided. Please enter a valid 4-digit year.');
            return 1;
        }

        $reportMappings = config('report-mapping');
        if (!$reportMappings || !is_array($reportMappings)) {
            $this->error('No report mappings found in config/report-mapping.php');
            return 1;
        }

        for ($month = 1; $month <= 12; $month++) {
            foreach ($reportMappings as $reportCode => $report) {
                // Replace this with your actual report generation logic
                $reportModel = ReportBatch::firstOrCreate(
                    [
                        'report_year' => $year,
                        'report_month' => $month,
                        'report_code' => $reportCode,
                    ],
                    [
                        'institution_code' => config('app.office_code'),
                        'branch_code' => config('app.branch_code'),
                    ]
                );
                $this->info("Generating report for Year: $year, Month: $month, Report Code: $reportModel->report_code, ID: $reportModel->id");
                // Example: ReportGenerator::generate($year, $month, $reportCode, $reportConfig);
            }
        }
        $this->info('Batch report generation completed.');
        return 0;
    }
}
