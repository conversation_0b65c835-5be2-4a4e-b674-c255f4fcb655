<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Check report templates
$templates = \App\Models\ReportTemplate::all();
echo "Report Templates:\n";
foreach ($templates as $template) {
    echo "- {$template->name} ({$template->alias}): {$template->description}\n";
}

// Check report elements count
$elementsCount = \App\Models\ReportElement::count();
echo "\nTotal Report Elements: {$elementsCount}\n";

// Check template details count for each template
echo "\nTemplate Details Count:\n";
foreach ($templates as $template) {
    $detailsCount = \App\Models\ReportTemplateDetail::where('report_template_id', $template->id)->count();
    echo "- {$template->alias}: {$detailsCount} elements\n";
}
