<?php

namespace App\Jobs;

use App\Models\Report;
use App\Reports\ReportFactory;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;

class ProcessReports implements ShouldQueue
{
    use Queueable, Dispatchable;

    protected $report;
    protected $periode;

    /**
     * Create a new job instance.
     */
    public function __construct(Report $report, string $periode)
    {
        $this->report = $report;
        $this->periode = $periode;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //
        $reportStrategy = ReportFactory::make($this->report->report_type_id);

        $reportStrategy->generateData($this->report, $this->periode);
    }
}
