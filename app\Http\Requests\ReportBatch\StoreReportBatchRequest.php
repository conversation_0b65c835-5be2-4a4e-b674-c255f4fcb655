<?php

namespace App\Http\Requests\ReportBatch;

use App\Models\ReportBatch;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreReportBatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $currentYear = (int) date('Y');
        $currentMonth = (int) date('n');
        
        return [
            'institution_code' => [
                'required',
                'string',
                'max:10',
            ],
            'branch_code' => [
                'required',
                'string',
                'max:10',
            ],
            'report_code' => [
                'required',
                'string',
                'max:10',
                Rule::in(array_keys(config('report-mapping', []))),
            ],
            'report_month' => [
                'required',
                'integer',
                'between:1,12',
            ],
            'report_year' => [
                'required',
                'integer',
                'between:2020,' . $currentYear,
            ],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'institution_code.required' => 'Institution code is required.',
            'institution_code.max' => 'Institution code must not exceed 10 characters.',
            'branch_code.required' => 'Branch code is required.',
            'branch_code.max' => 'Branch code must not exceed 10 characters.',
            'report_code.required' => 'Report code is required.',
            'report_code.in' => 'Invalid report code. Must be one of: ' . implode(', ', array_keys(config('report-mapping', []))),
            'report_month.required' => 'Report month is required.',
            'report_month.between' => 'Report month must be between 1 and 12.',
            'report_year.required' => 'Report year is required.',
            'report_year.between' => 'Report year must be between 2020 and current year.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check for duplicate report batch
            $exists = ReportBatch::where('institution_code', $this->institution_code)
                ->where('branch_code', $this->branch_code)
                ->where('report_code', $this->report_code)
                ->where('report_month', $this->report_month)
                ->where('report_year', $this->report_year)
                ->exists();

            if ($exists) {
                $validator->errors()->add(
                    'duplicate',
                    'A report batch for this period and report code already exists.'
                );
            }

            // Validate that the period is not in the future
            $currentYear = (int) date('Y');
            $currentMonth = (int) date('n');
            
            if ($this->report_year > $currentYear || 
                ($this->report_year == $currentYear && $this->report_month >= $currentMonth)) {
                $validator->errors()->add(
                    'future_period',
                    'Cannot create report batch for current or future periods.'
                );
            }
        });
    }

    /**
     * Get validated data with defaults.
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();
        
        return array_merge($validated, [
            'status' => ReportBatch::STATUS_PENDING,
            'record_count' => 0,
        ]);
    }
}
