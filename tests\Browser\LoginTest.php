<?php

use <PERSON><PERSON>\Dusk\Browser;

test('test_user_can_login_with_valid_credentials', function () {
    // Misal kita buat user di database (gunakan factory atau sejenisnya)
    $user = \App\Models\User::factory()->create([
        'email' => '<EMAIL>',
        'password' => bcrypt('secret123')
    ]);

    $this->browse(function (Browser $browser) use ($user) {
        $browser->visit('/login')
            ->type('email', $user->email)
            ->type('password', 'secret123')
            ->press('Login')
            ->assertPathIs('/home'); // asersi keberhasilan login

        // Ambil screenshot setelah sukses login
        $browser->screenshot('user_login_success');
    });
});

test(
    'test_user_cannot_login_with_invalid_credentials',
    function () {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                ->type('email', '<EMAIL>')
                ->type('password', 'wrongpass')
                ->press('Login')
                ->assertSee('These credentials do not match our records.'); // pesan error

            // Ambil screenshot saat gagal login
            $browser->screenshot('user_login_failure');
        });
    }
);
