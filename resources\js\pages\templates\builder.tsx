import { Head } from '@inertiajs/react';
import { <PERSON> } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
    useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ArrowLeft, GripVertical, Plus, Save, Trash2 } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import axios from 'axios';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface ReportElement {
    id: number;
    label: string;
    field: string;
    type: string;
}

interface TemplateDetail {
    id: number;
    report_template_id: number;
    report_element_id: number;
    order: number;
    element: ReportElement;
}

interface Template {
    id: number;
    name: string;
    alias: string;
    description: string;
    details: TemplateDetail[];
}

interface TemplateBuilderProps {
    template: Template;
    availableElements: ReportElement[];
}

// Sortable item component
function SortableItem({ detail, onRemove }: { detail: TemplateDetail; onRemove: (id: number) => void }) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition
    } = useSortable({ id: detail.id.toString() });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            className="bg-card border rounded-md p-3 mb-2 flex items-center gap-2"
        >
            <div
                {...attributes}
                {...listeners}
                className="cursor-grab active:cursor-grabbing p-1 flex items-center"
            >
                <span className="text-md text-muted-foreground mr-1">{detail.order}.</span>
                
                <GripVertical className="h-5 w-5 text-muted-foreground" />
                <span className="sr-only">Drag</span>
            </div>
            <div className="flex-1">
                <div className="font-medium">{detail.element.label}</div>
                <div className="text-sm text-muted-foreground">{detail.element.field}</div>
            </div>
            <Badge variant="outline" className="mr-2">
                {detail.element.type}
            </Badge>
            <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                onClick={() => onRemove(detail.id)}
            >
                <Trash2 className="h-4 w-4" />
                <span className="sr-only">Remove</span>
            </Button>
        </div>
    );
}

export default function TemplateBuilder({ template, availableElements }: TemplateBuilderProps) {
    const [items, setItems] = useState<TemplateDetail[]>([]);
    const [selectedElement, setSelectedElement] = useState<string>('');
    const [availableElementsList, setAvailableElementsList] = useState<ReportElement[]>([]);
    const [isAddingElement, setIsAddingElement] = useState(false);
    const [isRemovingElement, setIsRemovingElement] = useState(false);
    const [isSavingOrder, setIsSavingOrder] = useState(false);

    // No form instance needed, using axios directly

    // Initialize items from template details
    useEffect(() => {
        if (template.details) {
            setItems(template.details.sort((a, b) => a.order - b.order));
        }
    }, [template]);

    // Initialize available elements
    useEffect(() => {
        if (availableElements) {
            setAvailableElementsList(availableElements);
        }
    }, [availableElements]);

    // Add element to template
    const addElement = () => {
        if (!selectedElement) return;

        setIsAddingElement(true);

        axios.post(route('templates.elements.add', { template: template.id }), {
            report_element_id: selectedElement
        })
            .then(response => {
                // Add the new detail to the items list
                const newDetail = response.data.detail;
                setItems(prevItems => [...prevItems, newDetail]);

                // Remove the element from available elements
                setAvailableElementsList(prevElements =>
                    prevElements.filter(element => element.id.toString() !== selectedElement)
                );

                // Reset selected element
                setSelectedElement('');

                toast.success("Element added to template");
                setIsAddingElement(false);
            })
            .catch(error => {
                toast.error(error.response?.data?.message || "Failed to add element");
                setIsAddingElement(false);
            });
    };

    // Remove element from template
    const removeElement = (detailId: number) => {
        setIsRemovingElement(true);

        // Find the detail to be removed
        const detailToRemove = items.find(item => item.id === detailId);
        if (!detailToRemove) {
            setIsRemovingElement(false);
            return;
        }

        axios.delete(route('templates.elements.remove', { template: template.id, detail_id: detailId }))
            .then(() => {
                // Remove the detail from items
                setItems(prevItems => prevItems.filter(item => item.id !== detailId));

                // Add the element back to available elements
                if (detailToRemove.element) {
                    setAvailableElementsList(prevElements => [
                        ...prevElements,
                        detailToRemove.element
                    ]);
                }

                toast.success("Element removed from template");
                setIsRemovingElement(false);
            })
            .catch(error => {
                toast.error(error.response?.data?.message || "Failed to remove element");
                setIsRemovingElement(false);
            });
    };

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            setItems((items) => {
                const oldIndex = items.findIndex(item => item.id.toString() === active.id);
                const newIndex = items.findIndex(item => item.id.toString() === over.id);

                const newItems = arrayMove(items, oldIndex, newIndex);

                // Update order property
                return newItems.map((item, index) => ({
                    ...item,
                    order: index + 1
                }));
            });
        }
    };

    const saveOrder = () => {
        setIsSavingOrder(true);

        axios.post(route('templates.elements.reorder', { template: template.id }), {
            elements: items.map((item, index) => ({
                id: item.id,
                order: index + 1
            }))
        })
            .then(() => {
                toast.success("Template elements reordered successfully");
                setIsSavingOrder(false);
            })
            .catch(error => {
                toast.error(error.response?.data?.message || "Failed to save the new order");
                setIsSavingOrder(false);
            });
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Templates',
            href: '/templates',
        },
        {
            title: template.name,
            href: route('templates.show', { template: template.id }),
        },
        {
            title: 'Builder',
            href: route('templates.builder', { template: template.id }),
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Template Builder - ${template.name}`} />

            <div className="flex flex-col gap-6 p-4">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">{template.name} Builder</h1>
                        <p className="text-muted-foreground">{template.alias} - Drag and drop to reorder elements</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href={route('templates.show', { template: template.id })}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Template
                            </Link>
                        </Button>
                        <Button onClick={saveOrder} disabled={isSavingOrder}>
                            <Save className="mr-2 h-4 w-4" />
                            Save Order
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
                    <div className="md:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Template Elements</CardTitle>
                                <CardDescription>
                                    Drag and drop elements to reorder them
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {items.length === 0 ? (
                                    <div className="text-center py-8 border border-dashed rounded-md">
                                        <p className="text-muted-foreground">No elements added to this template yet.</p>
                                        <p className="text-sm text-muted-foreground mt-1">Add elements from the panel on the right.</p>
                                    </div>
                                ) : (
                                    <DndContext
                                        sensors={sensors}
                                        collisionDetection={closestCenter}
                                        onDragEnd={handleDragEnd}
                                    >
                                        <SortableContext
                                            items={items.map(item => item.id.toString())}
                                            strategy={verticalListSortingStrategy}
                                        >
                                            {items.map((detail) => (
                                                <SortableItem
                                                    key={detail.id}
                                                    detail={detail}
                                                    onRemove={removeElement}
                                                />
                                            ))}
                                        </SortableContext>
                                    </DndContext>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    <div>
                        <Card>
                            <CardHeader>
                                <CardTitle>Available Elements</CardTitle>
                                <CardDescription>
                                    Add elements to your template
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {/* <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="element">Select Element</Label>
                                        <Select
                                            value={selectedElement}
                                            onValueChange={setSelectedElement}
                                        >
                                            <SelectTrigger id="element">
                                                <SelectValue placeholder="Select an element" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {availableElementsList.map((element) => (
                                                    <SelectItem key={element.id} value={element.id.toString()}>
                                                        {element.label} ({element.field})
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button
                                        className="w-full"
                                        disabled={!selectedElement || isAddingElement}
                                        onClick={addElement}
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Add Element
                                    </Button>
                                </div> */}

                                <Separator className="my-2" />

                                <div className="space-y-2">
                                    {availableElementsList.map((element) => (
                                        <div key={element.id} className="flex justify-between items-center p-2 border rounded-md text-sm">
                                            <div>
                                                <div className="font-medium">{element.label}</div>
                                                <div className="text-xs text-muted-foreground">{element.field}</div>
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-8 w-8 p-0"
                                                onClick={() => {
                                                    // We need to set the element and then add it in a separate step
                                                    // to ensure the state is updated before calling addElement
                                                    setSelectedElement(element.id.toString());
                                                    // Use a direct approach instead of relying on state updates
                                                    setIsAddingElement(true);
                                                    const elementId = element.id.toString();

                                                    axios.post(route('templates.elements.add', { template: template.id }), {
                                                        report_element_id: elementId
                                                    })
                                                        .then(response => {
                                                            // Add the new detail to the items list
                                                            const newDetail = response.data.detail;
                                                            setItems(prevItems => [...prevItems, newDetail]);

                                                            // Remove the element from available elements
                                                            setAvailableElementsList(prevElements =>
                                                                prevElements.filter(e => e.id.toString() !== elementId)
                                                            );

                                                            toast.success("Element added to template");
                                                            setIsAddingElement(false);
                                                        })
                                                        .catch(error => {
                                                            toast.error(error.response?.data?.message || "Failed to add element");
                                                            setIsAddingElement(false);
                                                        });
                                                }}
                                                disabled={isAddingElement}
                                            >
                                                <Plus className="h-4 w-4" />
                                                <span className="sr-only">Add</span>
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                            <CardFooter>
                                <Button variant="outline" asChild className="w-full">
                                    <Link href={route('elements.create')}>
                                        Create New Element
                                    </Link>
                                </Button>
                            </CardFooter>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
