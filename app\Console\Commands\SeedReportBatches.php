<?php

namespace App\Console\Commands;

use App\Models\ReportBatch;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SeedReportBatches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:seed-report {--year=2024 : Year to seed data for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed sample report batch data for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $year = (int) $this->option('year');
        $reportCodes = array_keys(config('report-mapping', []));
        $institutionCode = config('app.office_code', 'INST001');
        $branchCode = config('app.branch_code', 'BR001');

        $this->info("🌱 Seeding report batches for year {$year}...");

        $totalCreated = 0;
        $currentDate = Carbon::now();

        for ($month = 1; $month <= 12; $month++) {
            // Only create batches for past months
            $batchDate = Carbon::create($year, $month, 1);
            if ($batchDate->isFuture()) {
                continue;
            }

            foreach ($reportCodes as $reportCode) {
                // Check if batch already exists
                $exists = ReportBatch::where('institution_code', $institutionCode)
                    ->where('branch_code', $branchCode)
                    ->where('report_code', $reportCode)
                    ->where('report_month', $month)
                    ->where('report_year', $year)
                    ->exists();

                if ($exists) {
                    continue;
                }

                // Determine status based on month
                $status = $this->determineStatus($month, $currentDate->month, $year, $currentDate->year);
                $recordCount = $this->generateRecordCount($status);
                $errorMessage = $status === 'failed' ? 'Sample error: Data validation failed' : null;
                $processedAt = in_array($status, ['completed', 'failed']) ? $batchDate->addDays(rand(1, 5)) : null;

                $batch = ReportBatch::create([
                    'institution_code' => $institutionCode,
                    'branch_code' => $branchCode,
                    'report_code' => $reportCode,
                    'report_month' => $month,
                    'report_year' => $year,
                    'status' => $status,
                    'record_count' => $recordCount,
                    'error_message' => $errorMessage,
                    'processed_at' => $processedAt,
                    'validation_results' => $this->generateValidationResults($status),
                ]);

                $totalCreated++;
                $this->line("✅ Created {$reportCode} batch for {$batchDate->format('F Y')} - Status: {$status}");
            }
        }

        $this->info("\n🎉 Successfully created {$totalCreated} report batches!");
        
        // Show summary
        $this->showSummary($year);

        return 0;
    }

    /**
     * Determine status based on month and current date
     */
    private function determineStatus(int $month, int $currentMonth, int $year, int $currentYear): string
    {
        // If it's a future month, return pending
        if ($year > $currentYear || ($year === $currentYear && $month >= $currentMonth)) {
            return ReportBatch::STATUS_PENDING;
        }

        // For past months, randomly assign status with weighted probability
        $rand = rand(1, 100);
        
        if ($rand <= 70) {
            return ReportBatch::STATUS_COMPLETED;
        } elseif ($rand <= 85) {
            return ReportBatch::STATUS_PENDING;
        } elseif ($rand <= 95) {
            return ReportBatch::STATUS_PROCESSING;
        } else {
            return ReportBatch::STATUS_FAILED;
        }
    }

    /**
     * Generate record count based on status
     */
    private function generateRecordCount(string $status): int
    {
        switch ($status) {
            case ReportBatch::STATUS_COMPLETED:
                return rand(500, 5000);
            case ReportBatch::STATUS_PROCESSING:
                return rand(100, 2000);
            case ReportBatch::STATUS_FAILED:
                return rand(0, 500);
            default:
                return 0;
        }
    }

    /**
     * Generate validation results based on status
     */
    private function generateValidationResults(string $status): array
    {
        $baseResults = [
            'valid' => $status !== ReportBatch::STATUS_FAILED,
            'warnings' => [],
            'errors' => [],
            'previous_period_available' => rand(0, 1) === 1,
            'comparison_period_available' => rand(0, 1) === 1,
        ];

        if ($status === ReportBatch::STATUS_FAILED) {
            $baseResults['errors'] = [
                'Comparison period data not available',
                'Data integrity check failed'
            ];
        } elseif (rand(0, 1) === 1) {
            $baseResults['warnings'] = [
                'Previous period data not available for comparison'
            ];
        }

        return $baseResults;
    }

    /**
     * Show summary of created batches
     */
    private function showSummary(int $year): void
    {
        $stats = [
            'total' => ReportBatch::where('report_year', $year)->count(),
            'completed' => ReportBatch::where('report_year', $year)->where('status', ReportBatch::STATUS_COMPLETED)->count(),
            'pending' => ReportBatch::where('report_year', $year)->where('status', ReportBatch::STATUS_PENDING)->count(),
            'processing' => ReportBatch::where('report_year', $year)->where('status', ReportBatch::STATUS_PROCESSING)->count(),
            'failed' => ReportBatch::where('report_year', $year)->where('status', ReportBatch::STATUS_FAILED)->count(),
        ];

        $this->info("\n📊 Summary for {$year}:");
        $this->table(
            ['Status', 'Count', 'Percentage'],
            [
                ['Total', $stats['total'], '100%'],
                ['Completed', $stats['completed'], $stats['total'] > 0 ? round(($stats['completed'] / $stats['total']) * 100, 1) . '%' : '0%'],
                ['Pending', $stats['pending'], $stats['total'] > 0 ? round(($stats['pending'] / $stats['total']) * 100, 1) . '%' : '0%'],
                ['Processing', $stats['processing'], $stats['total'] > 0 ? round(($stats['processing'] / $stats['total']) * 100, 1) . '%' : '0%'],
                ['Failed', $stats['failed'], $stats['total'] > 0 ? round(($stats['failed'] / $stats['total']) * 100, 1) . '%' : '0%'],
            ]
        );
    }
}
