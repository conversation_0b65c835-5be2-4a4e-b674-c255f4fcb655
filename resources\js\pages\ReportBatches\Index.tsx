import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Download, 
  Eye, 
  Edit, 
  Trash2,
  BarChart3,
  RefreshCw
} from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

import { 
  ReportBatch, 
  ReportBatchFilters, 
  ReportBatchFilterOptions,
  STATUS_COLORS,
  MONTH_NAMES 
} from '@/types/report';
import { PaginatedData } from '@/types';

interface Props {
  reportBatches: PaginatedData<ReportBatch>;
  filters: ReportBatchFilters;
  filterOptions: ReportBatchFilterOptions;
}

export default function ReportBatchesIndex({ reportBatches, filters, filterOptions }: Props) {
  const [selectedBatches, setSelectedBatches] = useState<number[]>([]);
  const [localFilters, setLocalFilters] = useState<ReportBatchFilters>(filters);

  const handleFilterChange = (key: keyof ReportBatchFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    
    router.get(route('report.index'), newFilters, {
      preserveState: true,
      replace: true,
    });
  };

  const handleSearch = (search: string) => {
    handleFilterChange('search', search);
  };

  const handleSelectBatch = (batchId: number, checked: boolean) => {
    if (checked) {
      setSelectedBatches([...selectedBatches, batchId]);
    } else {
      setSelectedBatches(selectedBatches.filter(id => id !== batchId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedBatches(reportBatches.data.map(batch => batch.id));
    } else {
      setSelectedBatches([]);
    }
  };

  const handleBulkExport = (format: 'json' | 'csv') => {
    if (selectedBatches.length === 0) {
      toast.error('Please select at least one report batch to export.');
      return;
    }

    router.post(route('report.bulk.export'), {
      report_batch_ids: selectedBatches,
      format: format,
    }, {
      onSuccess: () => {
        toast.success(`Bulk export (${format.toUpperCase()}) initiated successfully.`);
        setSelectedBatches([]);
      },
      onError: () => {
        toast.error('Failed to initiate bulk export.');
      },
    });
  };

  const handleDelete = (batch: ReportBatch) => {
    if (batch.status === 'completed') {
      toast.error('Cannot delete completed report batches.');
      return;
    }

    if (confirm(`Are you sure you want to delete the report batch for ${batch.period_name}?`)) {
      router.delete(route('report.destroy', batch.id), {
        onSuccess: () => {
          toast.success('Report batch deleted successfully.');
        },
        onError: () => {
          toast.error('Failed to delete report batch.');
        },
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const color = STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'gray';
    return (
      <Badge variant={color === 'green' ? 'default' : 'secondary'} className={`bg-${color}-100 text-${color}-800`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <AppLayout>
      <Head title="Report Batches" />

      <div className="space-y-6 md:px-2">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Report Batches</h1>
            <p className="text-muted-foreground">
              Manage and monitor SLIK report batch generation
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild variant="outline">
              <Link href={route('report.dashboard')}>
                <BarChart3 className="h-4 w-4 mr-2" />
                Dashboard
              </Link>
            </Button>
            <Button asChild>
              <Link href={route('report.create')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Report Batch
              </Link>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by code..."
                    value={localFilters.search || ''}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Year</label>
                <Select
                  value={localFilters.year?.toString() || ''}
                  onValueChange={(value) => handleFilterChange('year', value ? parseInt(value) : undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* <SelectItem>All Years</SelectItem> */}
                    {filterOptions.years.map(year => (
                      <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Month</label>
                <Select
                  value={localFilters.month?.toString() || ''}
                  onValueChange={(value) => handleFilterChange('month', value ? parseInt(value) : undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* <SelectItem value="">All Months</SelectItem> */}
                    {filterOptions.months.map(month => (
                      <SelectItem key={month} value={month.toString()}>
                        {MONTH_NAMES[month - 1]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Report Code</label>
                <Select
                  value={localFilters.report_code || ''}
                  onValueChange={(value) => handleFilterChange('report_code', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select report" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* <SelectItem value="">All Reports</SelectItem> */}
                    {filterOptions.reportCodes.map(code => (
                      <SelectItem key={code} value={code}>{code}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedBatches.length > 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {selectedBatches.length} batch(es) selected
                </span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBulkExport('json')}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export JSON
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBulkExport('csv')}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Report Batches Table */}
        <Card>
          <CardHeader>
            <CardTitle>Report Batches</CardTitle>
            <CardDescription>
              {reportBatches.total} total batches found
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedBatches.length === reportBatches.data.length && reportBatches.data.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Report Code</TableHead>
                  <TableHead>Institution</TableHead>
                  <TableHead>Branch</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Records</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reportBatches.data.map((batch) => (
                  <TableRow key={batch.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedBatches.includes(batch.id)}
                        onCheckedChange={(checked) => handleSelectBatch(batch.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{batch.period_name}</TableCell>
                    <TableCell>{batch.report_code}</TableCell>
                    <TableCell>{batch.institution_code}</TableCell>
                    <TableCell>{batch.branch_code}</TableCell>
                    <TableCell>{getStatusBadge(batch.status)}</TableCell>
                    <TableCell>{batch.record_count.toLocaleString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${batch.completion_percentage}%` }}
                          />
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {batch.completion_percentage}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(batch.created_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-1">
                        <Button asChild variant="ghost" size="sm">
                          <Link href={route('report.show', batch.id)}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button asChild variant="ghost" size="sm">
                          <Link href={route('report.edit', batch.id)}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        {batch.status !== 'completed' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(batch)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {reportBatches.data.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No report batches found.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
