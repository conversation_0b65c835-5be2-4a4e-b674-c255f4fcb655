import { Head, Link, router } from "@inertiajs/react";
import { FilePlus } from "lucide-react";
import { useState, useEffect } from "react";

import AppLayout from "@/layouts/app-layout";
import { type BreadcrumbItem } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { columns, type Element, elementTypes } from "./columns";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: "Elements",
    href: "/elements",
  },
];

interface ElementsDataTableIndexProps {
  elements: {
    data: Element[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  filters?: {
    search?: string;
    type?: string;
  };
}

export default function ElementsDataTableIndex({
  elements,
  filters = {},
}: ElementsDataTableIndexProps) {
  const [searchQuery, setSearchQuery] = useState(filters.search || "");
  const [typeFilter, setTypeFilter] = useState(filters.type || "");
  const [currentPage, setCurrentPage] = useState(elements.current_page);
  const [pageSize, setPageSize] = useState(elements.per_page);

  // Apply filters and update URL
  const applyFilters = (page = currentPage, perPage = pageSize) => {
    router.get(
      route("elements.data-table"),
      {
        search: searchQuery || undefined,
        type: typeFilter || undefined,
        page,
        per_page: perPage
      },
      {
        preserveState: true,
        replace: true,
        only: ["elements", "filters"]
      }
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setTypeFilter("");
    applyFilters(1);
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (value === "") {
      applyFilters(1);
    }
  };

  // Handle search submit
  const handleSearchSubmit = () => {
    applyFilters(1);
  };

  // Handle type filter change
  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value);
    applyFilters(1);
  };

  // Handle pagination change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    applyFilters(page);
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    applyFilters(1, size);
  };

  // Update state when props change
  useEffect(() => {
    setCurrentPage(elements.current_page);
    setPageSize(elements.per_page);
  }, [elements.current_page, elements.per_page]);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Report Elements" />

      <div className="flex flex-col gap-6 p-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Report Elements (Data Table)</h1>
            <div className="mt-1">
              <Link
                href={route("elements.index")}
                className="text-sm text-blue-600 hover:underline"
              >
                View with Standard Table
              </Link>
            </div>
          </div>
          <Button asChild>
            <Link href={route("elements.create")}>
              <FilePlus className="mr-2 h-4 w-4" />
              Create Element
            </Link>
          </Button>
        </div>

        {/* Active filters */}
        {(searchQuery || typeFilter) && (
          <div className="flex flex-wrap items-center gap-2 rounded-md border border-border bg-muted/40 px-3 py-2">
            <span className="text-sm font-medium">Active filters:</span>
            {searchQuery && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: {searchQuery}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 ml-1"
                  onClick={() => {
                    setSearchQuery("");
                    applyFilters(1);
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
            {typeFilter && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Type: {elementTypes.find(t => t.value === typeFilter)?.label}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 ml-1"
                  onClick={() => {
                    setTypeFilter("");
                    applyFilters(1);
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              Clear All
            </Button>
          </div>
        )}

        <DataTable
          columns={columns}
          data={elements.data}
          filterColumn="label"
          filterPlaceholder="Filter elements..."
          searchValue={searchQuery}
          onSearchChange={handleSearchChange}
          onSearchSubmit={handleSearchSubmit}
          typeFilter={typeFilter}
          onTypeFilterChange={handleTypeFilterChange}
          pagination={{
            currentPage: currentPage,
            totalPages: elements.last_page,
            totalItems: elements.total,
            pageSize: pageSize,
            siblingsCount: 1,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
          }}
        />
      </div>
    </AppLayout>
  );
}
